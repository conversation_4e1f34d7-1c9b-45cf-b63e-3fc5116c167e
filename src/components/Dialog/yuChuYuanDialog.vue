<template>
  <el-dialog
    :visible="visible"
    width="450px"
    append-to-body
    @close="updateVisible(false)"
    @open="initChuYuan()"
  >
    <span slot="title">
      <span class="dialog-title">
        <i class="el-icon-menu"></i>
        计划出院时间
      </span>
    </span>
    <span>
      <div class="dialog-component">
        <div class="search-head">
          <div style="width: 100px">计划出院时间</div>
          <el-date-picker
            v-model="chuYuanFormDate"
            style="width: 170px"
            type="date"
            value-format="yyyy-MM-dd hh:mm:ss"
            placeholder="选择日期"
          ></el-date-picker>
          <span v-if="isBatch" style="color: rgb(255, 35, 35); margin-right: 10px">批量</span>
          <template v-else>
            <template v-if="chuYuanDate">
              <span style="color: rgb(15, 173, 15); margin-right: 10px">
                已预出院({{ getDaysDiff() }})
              </span>
              <el-button type="text" @click="clearChuYuanDate">清空</el-button>
            </template>
            <span v-else style="color: rgb(255, 35, 35); margin-right: 0 10px">未保存</span>
          </template>
        </div>
      </div>
    </span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="updateChuYuanDate()">确 认</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getJiHuaCYSJ, clearJiHuaCYSJ, setJiHuaCYSJ } from '@/api/patient-info'

export default {
  name: 'YuChuYuanDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    zhuYuanIDList: {
      type: Array,
      default: () => []
    },
    isBatch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chuYuanFormDate: '',
      chuYuanDate: ''
    }
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    async clearChuYuanDate() {
      if (this.zhuYuanIDList.length) {
        const res = await clearJiHuaCYSJ({
          zhuYuanID: this.zhuYuanIDList[0]
        })
        if (res.hasError === 0) {
          this.$message.success('清空成功')
          this.updateVisible(false)
        }
      }
    },
    async updateChuYuanDate() {
      if (this.zhuYuanIDList.length) {
        if (this.isBatch) {
          let successCount = 0 //统计接口批量处理成功次数
          this.zhuYuanIDList.forEach(async (zhuYuanID) => {
            const res = await setJiHuaCYSJ({
              zhuYuanID: zhuYuanID,
              jiHuaCYSJ: this.chuYuanFormDate
            })
            if (res.hasError === 0) {
              successCount++
              if (this.zhuYuanIDList.length === successCount) {
                this.$message.success('批量设置成功')
                this.updateVisible(false)
              }
            }
          })
        } else {
          const res = await setJiHuaCYSJ({
            zhuYuanID: this.zhuYuanIDList[0],
            jiHuaCYSJ: this.chuYuanFormDate
          })
          if (res.hasError === 0) {
            this.$message.success('计划出院安排成功')
            this.updateVisible(false)
          }
        }
      }
    },
    //获取默认时间
    getDefDate() {
      let defTime = new Date()
      defTime.setDate(defTime.getDate() + 1)
      const year = defTime.getFullYear()
      const month = defTime.getMonth() + 1
      const day = defTime.getDate()
      return (
        year +
        '-' +
        (month < 10 ? '0' + month : month) +
        '-' +
        (day < 10 ? '0' + day : day) +
        ' ' +
        '00:00:00'
      )
    },
    //获取距今天数差
    getDaysDiff() {
      // 将日期转换为毫秒
      const diffInMs = Math.abs(new Date(this.chuYuanDate) - new Date())
      // 将毫秒转换为天数
      const diffInDays = diffInMs / (1000 * 60 * 60 * 24)
      return Math.floor(diffInDays)
    },
    async initChuYuan() {
      this.chuYuanFormDate = this.getDefDate()
      if (!this.isBatch && this.zhuYuanIDList.length) {
        const res = await getJiHuaCYSJ({
          zhuYuanID: this.zhuYuanIDList[0]
        })
        if (res.hasError === 0) {
          this.chuYuanDate = res.data
          if (res.data) {
            this.chuYuanFormDate = res.data
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-title {
  font-size: 16px;
}

.dialog-component {
  padding: 0 10px;
  .search-head {
    display: flex;
    align-items: center;
    label {
      margin: 0 10px 0 10px;
    }
  }
}

:deep(.el-dialog__footer) {
  border-top: none;
}
</style>
