import router from './router'
import store from './store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'
import { omit } from '@/utils/tools'
import { getAppWhitelist } from '@/api/basic'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/qrCodeLoading', '/login', '/yuLan/biaoDan']
// no redirect whitelist

router.beforeEach(async (to, from, next) => {
  NProgress.start()

  if (
    !store?.state?.app?.noNeedVerifyAppList ||
    store?.state?.app?.noNeedVerifyAppList?.length <= 0
  ) {
    //获取接口领域白名单
    getAppWhitelist().then((rsp) => {
      store.dispatch('app/setNoNeedVerifyAppList', rsp.data)
    })
  }
  document.title = getPageTitle(to.meta.title)
  const token = getToken()

  // 如果登陆成功则携带token
  if (to.query && to.query.auth_token) {
    await store.dispatch('user/saveAuth', to.query)
    // 是单点登录跳转 然后是锁屏状态->解锁
    if (store.getters.isLock) store.dispatch('lockscreen/setLock', false)
    // 跳转至干净的路由地址
    next({
      path: to.path,
      query: omit(to.query, ['access_dm', 'access_code', 'auth_token', 'refresh_token', 'uuid'])
    })
    NProgress.done()
    return
  }

  // 白名单地址特殊处理，已登录直接跳转根目录，无token放行
  if (whiteList.includes(to.path)) {
    if (token) next({ path: '/' })
    else next()
    NProgress.done()
    return
  }

  if (!token) {
    next(`/login?redirect=${to.path}`)
    NProgress.done()
    return
  }

  // 正常判断
  if (token) {
    if (store.getters.sidebarRouters.length === 0) {
      try {
        // 判断当前用户是否已拉取完user_info信息
        await store.dispatch('user/getInfo')
        const accessRoutes = await store.dispatch('permission/GenerateRoutes')
        if (accessRoutes.length && accessRoutes.length > 0) {
          router.addRoutes(accessRoutes) // 动态添加可访问路由表
          console.log('🚀 ~ router.beforeEach ~ router:', router)

          next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
        } else {
          console.error('无权限退出')
          await store.dispatch('user/logOut', '暂无权限,请联系管理员授权后再次登陆!')
        }
      } catch (error) {
        console.error('异常退出', error)
        if (error) {
          await store.dispatch('user/logOut', error.message)
        } else {
          await store.dispatch('user/logOut', '获取用户信息失败,请重新登陆!')
        }
      } finally {
        NProgress.done()
      }
    } else {
      next()
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
