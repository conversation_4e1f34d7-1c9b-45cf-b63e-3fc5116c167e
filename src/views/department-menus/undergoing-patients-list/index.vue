<template>
  <div class="container">
    <div class="header">
      <div class="query-word">时间范围：</div>
      <el-date-picker
        v-model="zhuanKeSJ"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="changeDay"
      ></el-date-picker>
      <div class="query-word">来源科室:</div>
      <el-select v-model="kuaKeID" filterable>
        <el-option
          v-for="item in ZhiLiaoZuList"
          :key="item.buMenID"
          :label="item.buMenMC"
          :value="item.buMenID"
        ></el-option>
      </el-select>
      <el-checkbox v-model="yuWoYG" style="margin: 0 10px 0 20px">与我有关</el-checkbox>
      <div class="button">
        <el-button type="primary" @click="getListByBingLiID">查询</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">专科跨科治疗列表</div>
      </div>
      <div class="table">
        <el-table max-height="688" border stripe :data="patientData" style="width: 1598px">
          <el-table-column prop="bingRenXM" width="120" label="病人姓名"></el-table-column>
          <el-table-column prop="bingAnHao" width="130" label="病案号"></el-table-column>
          <el-table-column prop="zhuanKeMC" width="170" label="来源专科"></el-table-column>
          <el-table-column prop="kuaKeYSMC" width="120" label="指定医生"></el-table-column>
          <el-table-column prop="chuZhiYSMC" width="120" label="录入人员"></el-table-column>
          <el-table-column prop="shouCiLRSJ" width="170" label="首次录入时间"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" width="100" label="在院状态">
            <template #default="{ row }">
              <span>{{ row.zhuangTaiBZ == 1 ? '出院' : '在院' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="xiuGaiSJ" width="170" label="最后修改时间"></el-table-column>
          <el-table-column prop="jieShuSJ" width="170" label="权限结束时间"></el-table-column>
          <el-table-column prop="beiZhu" width="220" label="备注"></el-table-column>
          <el-table-column prop="caoZuo" width="106" align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getListByCondition, getZhuanKeList } from '@/api/specialized-patients'
import { format, subMonths } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      zhuanKeSJ: [format(subMonths(new Date(), 1), 'yyyy-MM-dd'), format(new Date(), 'yyyy-MM-dd')], // 默认值
      kaiShiSJ: format(subMonths(new Date(), 1), 'yyyy-MM-dd'), //开始时间
      jieShuSJ: format(new Date(), 'yyyy-MM-dd'), //结束时间
      patientData: [],
      kuaKeID: '',
      ZhiLiaoZuList: [],
      yuWoYG: true
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit
    })
  },
  async mounted() {
    await this.getListByBingLiID()
    await this.getZhuanKeList()
  },
  methods: {
    // 获取列表初始化接口
    async getListByBingLiID() {
      try {
        const res = await getListByCondition({
          kaiShiSJ: this.kaiShiSJ + ' 00:00:00',
          jieShuSJ: this.jieShuSJ + ' 23:59:59',
          kuaKeID: this.kuaKeID,
          yuWoYG: this.yuWoYG,
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.patientData = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取专科治疗组列表
    async getZhuanKeList() {
      try {
        const res = await getZhuanKeList()
        if (res.hasError === 0) {
          this.ZhiLiaoZuList = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },

    changeDay(e) {
      this.kaiShiSJ = format(new Date(e[0]), 'yyyy-MM-dd')
      this.jieShuSJ = format(new Date(e[1]), 'yyyy-MM-dd')
    },

    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 740px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}

::v-deep .el-table__cell {
  padding-left: 8px;
}

::v-deep .el-table__cell:last-child {
  padding-left: 0px;
}

::v-deep .el-table th.el-table__cell,
.el-table td.el-table__cell {
  padding-top: 9px;
  padding-bottom: 9px;
}

::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
  padding-top: 4px;
  padding-bottom: 4px;
}
</style>
