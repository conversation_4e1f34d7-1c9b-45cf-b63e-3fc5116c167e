<template>
  <div style="width: 100%; height: 100%; background-color: #ffffff">
    <div style="width: 1680px; margin: 0 auto; height: 100%">
      <div v-if="dialogVisible === 'LIST'" class="container">
        <div class="content">
          <el-tabs v-model="tabsName" type="card" style="height: 100%" @tab-click="tabsClick">
            <el-tab-pane label="全部" name="QB">
              <div class="header" style="border: 2px solid #ddd; margin-top: 12px">
                <div>
                  <div style="display: flex; align-items: center">
                    <div class="query-word">院区选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.yuanQuDM">
                        <el-radio label="55">龙港</el-radio>
                        <el-radio label="1">非龙港</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.huanZheLX">
                        <el-radio label="2">住院报告</el-radio>
                        <el-radio label="1">门诊报告</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告日期：</div>
                    <div class="query-value">
                      <el-date-picker
                        v-model="ruleForm.date"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                      ></el-date-picker>
                    </div>
                    <div class="query-word">审核状态：</div>
                    <div class="query-value">
                      <el-select v-model="ruleForm.shenHeZT">
                        <el-option label="全部" value="-1"></el-option>
                        <el-option label="通过" value="1"></el-option>
                        <el-option label="未通过" value="2"></el-option>
                        <el-option label="未审核" value="0"></el-option>
                      </el-select>
                    </div>
                  </div>
                  <div style="display: flex; align-items: center; margin-top: 4px">
                    <div class="query-word">上传状态：</div>
                    <div class="query-value">
                      <el-select v-model="ruleForm.shangChuanZT">
                        <el-option label="全部" value="0"></el-option>
                        <el-option label="已上传" value="1"></el-option>
                        <el-option label="未上传" value="2"></el-option>
                        <el-option label="上传失败" value="3"></el-option>
                      </el-select>
                    </div>
                    <div class="query-word">病种：</div>
                    <div class="query-value">
                      <el-select v-model="icd['QB']" clearable :popper-append-to-body="false">
                        <el-option
                          v-for="data in baseInfo.zhenDuan"
                          :key="data.mingCheng"
                          :label="data.mingCheng"
                          :value="data.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="button">
                      <el-button type="primary" @click="query">查询</el-button>
                    </div>
                    <el-button type="primary" @click="setDZ">患者地址管理</el-button>
                  </div>
                </div>
              </div>
              <div class="content" style="padding: 0">
                <div class="content-header">
                  <div class="title">全部</div>
                </div>
                <div class="content-table">
                  <el-table
                    :data="tableData['QB']"
                    border
                    size="medium"
                    height="100%"
                    style="width: 100%"
                  >
                    <el-table-column prop="bianHao" label="序号"></el-table-column>
                    <el-table-column prop="xingMing" label="姓名"></el-table-column>
                    <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
                    <el-table-column prop="chuShengRQ" label="出生日期">
                      <template slot-scope="scope">
                        {{ scope.row.chuShengRQ === null ? '' : scope.row.chuShengRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="xianZhuXXDZ" label="现住地址"></el-table-column>
                    <el-table-column prop="jiBingMC" label="诊断" width="100"></el-table-column>
                    <el-table-column prop="keShiMC" label="科室"></el-table-column>
                    <el-table-column prop="baoKaYS" label="报卡医师"></el-table-column>
                    <el-table-column prop="baoKaRQ" label="报告日期">
                      <template slot-scope="scope">
                        {{ scope.row.baoKaRQ === null ? '' : scope.row.baoKaRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="zhuangTaiBZ" label="状态"></el-table-column>
                    <el-table-column prop="shangChuanBZ" label="上传"></el-table-column>
                    <el-table-column prop="guoJiaZDJG" label="国家诊断"></el-table-column>
                    <el-table-column prop="guoJiaBGJG" label="国家报告"></el-table-column>
                    <el-table-column label="操作" align="center" width="160">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="view(scope.$index, scope.row)">
                          查看
                        </el-button>
                        <el-divider direction="vertical"></el-divider>
                        <el-button type="text" size="mini" @click="onDel(scope.$index, scope.row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-pagination
                  :current-page.sync="pageIndex['QB']"
                  :page-size="pageSize"
                  layout="total, prev, pager, next"
                  :total="total['QB']"
                  @current-change="setPage"
                ></el-pagination>
              </div>
            </el-tab-pane>
            <el-tab-pane label="普通传染病病例管理" name="PT">
              <div class="header" style="border: 2px solid #ddd; margin-top: 12px">
                <div>
                  <div style="display: flex; align-items: center">
                    <div class="query-word">院区选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.yuanQuDM">
                        <el-radio label="55">龙港</el-radio>
                        <el-radio label="1">非龙港</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.huanZheLX">
                        <el-radio label="2">住院报告</el-radio>
                        <el-radio label="1">门诊报告</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告日期：</div>
                    <div class="query-value">
                      <el-date-picker
                        v-model="ruleForm.date"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                      ></el-date-picker>
                    </div>
                    <div class="query-word">审核状态：</div>
                    <div class="query-value">
                      <el-select v-model="ruleForm.shenHeZT">
                        <el-option label="全部" value="-1"></el-option>
                        <el-option label="通过" value="1"></el-option>
                        <el-option label="未通过" value="2"></el-option>
                        <el-option label="未审核" value="0"></el-option>
                      </el-select>
                    </div>
                  </div>
                  <div style="display: flex; align-items: center; margin-top: 4px">
                    <div class="query-word">上传状态：</div>
                    <div class="query-value">
                      <el-select v-model="ruleForm.shangChuanZT">
                        <el-option label="全部" value="0"></el-option>
                        <el-option label="已上传" value="1"></el-option>
                        <el-option label="未上传" value="2"></el-option>
                        <el-option label="上传失败" value="3"></el-option>
                      </el-select>
                    </div>
                    <div class="query-word">病种：</div>
                    <div class="query-value">
                      <el-select v-model="icd['PT']" clearable :popper-append-to-body="false">
                        <el-option
                          v-for="data in baseInfo.zhenDuan"
                          :key="data.mingCheng"
                          :label="data.mingCheng"
                          :value="data.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="button">
                      <el-button type="primary" @click="query">查询</el-button>
                    </div>
                    <el-button type="primary" @click="setDZ">患者地址管理</el-button>
                  </div>
                </div>
              </div>
              <div class="content" style="padding: 0">
                <div class="content-header">
                  <div class="title">普通传染病报告卡管理</div>
                </div>
                <div class="content-table">
                  <el-table
                    :data="tableData['PT']"
                    border
                    size="medium"
                    height="100%"
                    style="width: 100%"
                  >
                    <el-table-column prop="bianHao" label="序号"></el-table-column>
                    <el-table-column prop="xingMing" label="姓名"></el-table-column>
                    <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
                    <el-table-column prop="chuShengRQ" label="出生日期">
                      <template slot-scope="scope">
                        {{ scope.row.chuShengRQ === null ? '' : scope.row.chuShengRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="xianZhuXXDZ" label="现住地址"></el-table-column>
                    <el-table-column prop="jiBingMC" label="诊断" width="100"></el-table-column>
                    <el-table-column prop="keShiMC" label="科室"></el-table-column>
                    <el-table-column prop="baoKaYS" label="报卡医师"></el-table-column>
                    <el-table-column prop="baoKaRQ" label="报告日期">
                      <template slot-scope="scope">
                        {{ scope.row.baoKaRQ === null ? '' : scope.row.baoKaRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="zhuangTaiBZ" label="状态"></el-table-column>
                    <el-table-column prop="shangChuanBZ" label="上传"></el-table-column>
                    <el-table-column prop="guoJiaZDJG" label="国家诊断"></el-table-column>
                    <el-table-column prop="guoJiaBGJG" label="国家报告"></el-table-column>
                    <el-table-column label="操作" align="center" width="160">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="view(scope.$index, scope.row)">
                          查看
                        </el-button>
                        <el-divider direction="vertical"></el-divider>
                        <el-button type="text" size="mini" @click="onDel(scope.$index, scope.row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-pagination
                  :current-page.sync="pageIndex['PT']"
                  :page-size="pageSize"
                  layout="total, prev, pager, next"
                  :total="total['PT']"
                  @current-change="setPage"
                ></el-pagination>
              </div>
            </el-tab-pane>
            <el-tab-pane label="性病病例管理" name="XB">
              <div class="header" style="border: 2px solid #ddd; margin-top: 12px">
                <div>
                  <div style="display: flex; align-items: center">
                    <div class="query-word">院区选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.yuanQuDM">
                        <el-radio label="55">龙港</el-radio>
                        <el-radio label="1">非龙港</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.huanZheLX">
                        <el-radio label="2">住院报告</el-radio>
                        <el-radio label="1">门诊报告</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告日期：</div>
                    <div class="query-value">
                      <el-date-picker
                        v-model="ruleForm.date"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                      ></el-date-picker>
                    </div>
                    <div class="query-word">审核状态：</div>
                    <div class="query-value">
                      <el-select v-model="ruleForm.shenHeZT">
                        <el-option label="全部" value="-1"></el-option>
                        <el-option label="通过" value="1"></el-option>
                        <el-option label="未通过" value="2"></el-option>
                        <el-option label="未审核" value="0"></el-option>
                      </el-select>
                    </div>
                  </div>
                  <div style="display: flex; align-items: center; margin-top: 4px">
                    <div class="query-word">上传状态：</div>
                    <div class="query-value">
                      <el-select v-model="ruleForm.shangChuanZT">
                        <el-option label="全部" value="0"></el-option>
                        <el-option label="已上传" value="1"></el-option>
                        <el-option label="未上传" value="2"></el-option>
                        <el-option label="上传失败" value="3"></el-option>
                      </el-select>
                    </div>
                    <div class="query-word">病种：</div>
                    <div class="query-value">
                      <el-select v-model="icd['XB']" clearable :popper-append-to-body="false">
                        <el-option
                          v-for="data in baseInfo.zhenDuan"
                          :key="data.mingCheng"
                          :label="data.mingCheng"
                          :value="data.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="button">
                      <el-button type="primary" @click="query">查询</el-button>
                    </div>
                    <el-button type="primary" @click="setDZ">患者地址管理</el-button>
                  </div>
                </div>
              </div>
              <div class="content" style="padding: 0">
                <div class="content-header">
                  <div class="title">性病报告卡管理</div>
                </div>
                <div class="content-table">
                  <el-table
                    :data="tableData['XB']"
                    border
                    size="medium"
                    height="100%"
                    style="width: 100%"
                  >
                    <el-table-column prop="bianHao" label="序号"></el-table-column>
                    <el-table-column prop="xingMing" label="姓名"></el-table-column>
                    <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
                    <el-table-column prop="chuShengRQ" label="出生日期">
                      <template slot-scope="scope">
                        {{ scope.row.chuShengRQ === null ? '' : scope.row.chuShengRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="xianZhuXXDZ" label="现住地址"></el-table-column>
                    <el-table-column prop="jiBingMC" label="诊断" width="100"></el-table-column>
                    <el-table-column prop="keShiMC" label="科室"></el-table-column>
                    <el-table-column prop="baoKaYS" label="报卡医师"></el-table-column>
                    <el-table-column prop="baoKaRQ" label="报告日期">
                      <template slot-scope="scope">
                        {{ scope.row.baoKaRQ === null ? '' : scope.row.baoKaRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="zhuangTaiBZ" label="状态"></el-table-column>
                    <el-table-column prop="shangChuanBZ" label="上传"></el-table-column>
                    <el-table-column prop="guoJiaZDJG" label="国家诊断"></el-table-column>
                    <el-table-column prop="guoJiaBGJG" label="国家报告"></el-table-column>
                    <el-table-column label="操作" align="center" width="160">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="view(scope.$index, scope.row)">
                          查看
                        </el-button>
                        <el-divider direction="vertical"></el-divider>
                        <el-button type="text" size="mini" @click="onDel(scope.$index, scope.row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-pagination
                  :current-page.sync="pageIndex['XB']"
                  :page-size="pageSize"
                  layout="total, prev, pager, next"
                  :total="total['XB']"
                  @current-change="setPage"
                ></el-pagination>
              </div>
            </el-tab-pane>
            <el-tab-pane label="AFP病例管理" name="MBZ">
              <div class="header" style="border: 2px solid #ddd; margin-top: 12px">
                <div>
                  <div style="display: flex; align-items: center">
                    <div class="query-word">院区选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.yuanQuDM">
                        <el-radio label="55">龙港</el-radio>
                        <el-radio label="1">非龙港</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.huanZheLX">
                        <el-radio label="2">住院报告</el-radio>
                        <el-radio label="1">门诊报告</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告日期：</div>
                    <div class="query-value">
                      <el-date-picker
                        v-model="ruleForm.date"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                      ></el-date-picker>
                    </div>
                    <div class="query-word">审核状态：</div>
                    <div class="query-value">
                      <el-select v-model="ruleForm.shenHeZT">
                        <el-option label="全部" value="-1"></el-option>
                        <el-option label="通过" value="1"></el-option>
                        <el-option label="未通过" value="2"></el-option>
                        <el-option label="未审核" value="0"></el-option>
                      </el-select>
                    </div>
                  </div>
                  <div style="display: flex; align-items: center; margin-top: 4px">
                    <div class="query-word">上传状态：</div>
                    <div class="query-value">
                      <el-select v-model="ruleForm.shangChuanZT">
                        <el-option label="全部" value="0"></el-option>
                        <el-option label="已上传" value="1"></el-option>
                        <el-option label="未上传" value="2"></el-option>
                        <el-option label="上传失败" value="3"></el-option>
                      </el-select>
                    </div>
                    <div class="query-word">病种：</div>
                    <div class="query-value">
                      <el-select v-model="icd['MBZ']" clearable :popper-append-to-body="false">
                        <el-option
                          v-for="data in baseInfo.zhenDuan"
                          :key="data.mingCheng"
                          :label="data.mingCheng"
                          :value="data.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="button">
                      <el-button type="primary" @click="query">查询</el-button>
                    </div>
                    <el-button type="primary" @click="setDZ">患者地址管理</el-button>
                  </div>
                </div>
              </div>
              <div class="content" style="padding: 0">
                <div class="content-header">
                  <div class="title">AFP报告卡管理</div>
                </div>
                <div class="content-table">
                  <el-table
                    :data="tableData['MBZ']"
                    border
                    size="medium"
                    height="100%"
                    style="width: 100%"
                  >
                    <el-table-column prop="bianHao" label="序号"></el-table-column>
                    <el-table-column prop="xingMing" label="姓名"></el-table-column>
                    <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
                    <el-table-column prop="chuShengRQ" label="出生日期">
                      <template slot-scope="scope">
                        {{ scope.row.chuShengRQ === null ? '' : scope.row.chuShengRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="xianZhuXXDZ" label="现住地址"></el-table-column>
                    <el-table-column prop="jiBingMC" label="诊断" width="100"></el-table-column>
                    <el-table-column prop="keShiMC" label="科室"></el-table-column>
                    <el-table-column prop="baoKaYS" label="报卡医师"></el-table-column>
                    <el-table-column prop="baoKaRQ" label="报告日期">
                      <template slot-scope="scope">
                        {{ scope.row.baoKaRQ === null ? '' : scope.row.baoKaRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="zhuangTaiBZ" label="状态"></el-table-column>
                    <el-table-column prop="shangChuanBZ" label="上传"></el-table-column>
                    <el-table-column prop="guoJiaZDJG" label="国家诊断"></el-table-column>
                    <el-table-column prop="guoJiaBGJG" label="国家报告"></el-table-column>
                    <el-table-column label="操作" align="center" width="160">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="view(scope.$index, scope.row)">
                          查看
                        </el-button>
                        <el-divider direction="vertical"></el-divider>
                        <el-button type="text" size="mini" @click="onDel(scope.$index, scope.row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-pagination
                  :current-page.sync="pageIndex['MBZ']"
                  :page-size="pageSize"
                  layout="total, prev, pager, next"
                  :total="total['MBZ']"
                  @current-change="setPage"
                ></el-pagination>
              </div>
            </el-tab-pane>
            <el-tab-pane label="手足口病例管理" name="SZKB">
              <div class="header" style="border: 2px solid #ddd; margin-top: 12px">
                <div>
                  <div style="display: flex; align-items: center">
                    <div class="query-word">院区选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.yuanQuDM">
                        <el-radio label="55">龙港</el-radio>
                        <el-radio label="1">非龙港</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.huanZheLX">
                        <el-radio label="2">住院报告</el-radio>
                        <el-radio label="1">门诊报告</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告日期：</div>
                    <div class="query-value">
                      <el-date-picker
                        v-model="ruleForm.date"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                      ></el-date-picker>
                    </div>
                    <div class="query-word">审核状态：</div>
                    <div class="query-value">
                      <el-select v-model="ruleForm.shenHeZT">
                        <el-option label="全部" value="-1"></el-option>
                        <el-option label="通过" value="1"></el-option>
                        <el-option label="未通过" value="2"></el-option>
                        <el-option label="未审核" value="0"></el-option>
                      </el-select>
                    </div>
                  </div>
                  <div style="display: flex; align-items: center; margin-top: 4px">
                    <div class="query-word">上传状态：</div>
                    <div class="query-value">
                      <el-select v-model="ruleForm.shangChuanZT">
                        <el-option label="全部" value="0"></el-option>
                        <el-option label="已上传" value="1"></el-option>
                        <el-option label="未上传" value="2"></el-option>
                        <el-option label="上传失败" value="3"></el-option>
                      </el-select>
                    </div>
                    <div class="query-word">病种：</div>
                    <div class="query-value">
                      <el-select v-model="icd['SZKB']" clearable :popper-append-to-body="false">
                        <el-option
                          v-for="data in baseInfo.zhenDuan"
                          :key="data.mingCheng"
                          :label="data.mingCheng"
                          :value="data.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="button">
                      <el-button type="primary" @click="query">查询</el-button>
                    </div>
                    <el-button type="primary" @click="setDZ">患者地址管理</el-button>
                  </div>
                </div>
              </div>
              <div class="content" style="padding: 0">
                <div class="content-header">
                  <div class="title">手足口报告卡管理</div>
                </div>
                <div class="content-table">
                  <el-table
                    :data="tableData['SZKB']"
                    border
                    size="medium"
                    height="100%"
                    style="width: 100%"
                  >
                    <el-table-column prop="bianHao" label="序号"></el-table-column>
                    <el-table-column prop="xingMing" label="姓名"></el-table-column>
                    <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
                    <el-table-column prop="chuShengRQ" label="出生日期">
                      <template slot-scope="scope">
                        {{ scope.row.chuShengRQ === null ? '' : scope.row.chuShengRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="xianZhuXXDZ" label="现住地址"></el-table-column>
                    <el-table-column prop="jiBingMC" label="诊断" width="100"></el-table-column>
                    <el-table-column prop="keShiMC" label="科室"></el-table-column>
                    <el-table-column prop="baoKaYS" label="报卡医师"></el-table-column>
                    <el-table-column prop="baoKaRQ" label="报告日期">
                      <template slot-scope="scope">
                        {{ scope.row.baoKaRQ === null ? '' : scope.row.baoKaRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="zhuangTaiBZ" label="状态"></el-table-column>
                    <el-table-column prop="shangChuanBZ" label="上传"></el-table-column>
                    <el-table-column prop="guoJiaZDJG" label="国家诊断"></el-table-column>
                    <el-table-column prop="guoJiaBGJG" label="国家报告"></el-table-column>
                    <el-table-column label="操作" align="center" width="160">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="view(scope.$index, scope.row)">
                          查看
                        </el-button>
                        <el-divider direction="vertical"></el-divider>
                        <el-button type="text" size="mini" @click="onDel(scope.$index, scope.row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-pagination
                  :current-page.sync="pageIndex['SZKB']"
                  :page-size="pageSize"
                  layout="total, prev, pager, next"
                  :total="total['SZKB']"
                  @current-change="setPage"
                ></el-pagination>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div v-else-if="dialogVisible === 'XX'" style="height: 100%">
        <infectious-disease-report-card
          :patient="cardData"
          @childEvent="handleChildData"
        ></infectious-disease-report-card>
      </div>
      <div v-else-if="dialogVisible === 'DZ'" style="height: 100%">
        <address-management :patient="dzData" @childEvent="handleChildData"></address-management>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  deleteReport,
  getBaseInfo,
  queryInfectiousDiseaseReport,
  viewInfectiousDiseaseReport,
  viewZlReport
} from '@/api/report-card'
import { format } from 'date-fns'

import AddressManagement from '@/views/department-menus/report-card/address-management/index.vue'
import InfectiousDiseaseReportCard from '@/views/patient-inside/medical-related-report-card/InfectiousDiseaseReportCard.vue'

export default {
  name: 'VerifyCheckOrdersStatus',
  components: {
    // eslint-disable-next-line vue/no-unused-components
    InfectiousDiseaseReportCard,
    AddressManagement
  },
  data() {
    return {
      tabsName: 'QB',
      cardData: {},
      dzData: {},
      dialogVisible: 'LIST',
      ruleForm: {
        date: [],
        chuanRanBingFL: 'all',
        // zhuangTaiBZ: '1', //状态标志
        huanZheLX: '2', //患者类型
        bingZhong: '', //病种
        // shenFenZheng: '', //身份证
        shenHeZT: '1', //审核状态
        shangChuanZT: '0', //上传状态
        kaiShiSJ: '', //开始时间
        jieShuSJ: '', //结束时间
        curr_page: 0, //当前页码
        count: 10, //每页数量
        yuanQuDM: '1'
      },
      icd: {
        QB: '',
        PT: '',
        XB: '',
        MBZ: '',
        SZKB: ''
      },
      tableData: {
        QB: [],
        PT: [],
        XB: [],
        MBZ: [],
        SZKB: []
      },
      baseInfo: {},
      total: {
        QB: 0,
        PT: 0,
        XB: 0,
        MBZ: 0,
        SZKB: 0
      },
      pageIndex: {
        QB: 1,
        PT: 1,
        XB: 1,
        MBZ: 1,
        SZKB: 1
      },
      pageSize: 10
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      const now = new Date()
      const date1 = new Date().setMonth(new Date().getMonth() - 1)
      const nowStr = format(now, 'yyyy-MM-dd')
      const date1Str = format(date1, 'yyyy-MM-dd')
      this.ruleForm['date'] = [date1Str, nowStr]
      await this.getReportCard()
    },
    async setPage(pageIndex) {
      this.pageIndex[this.tabsName] = pageIndex
      await this.query()
    },
    tabsClick(tab, event) {
      console.log(tab, event)
    },
    async getReportCard() {
      let res = {}
      res = await getBaseInfo({})
      this.baseInfo = res.data
    },
    async query() {
      let reqData = JSON.parse(JSON.stringify(this.ruleForm))
      reqData['curr_page'] = this.pageIndex[this.tabsName]
      reqData['count'] = this.pageSize
      if (reqData.date) {
        reqData['kaiShiSJ'] = reqData.date[0] + ' 00:00:00'
        let jieShuSJ = new Date(reqData.date[1])
        jieShuSJ.setDate(jieShuSJ.getDate() + 1)
        reqData['jieShuSJ'] = format(jieShuSJ, 'yyyy-MM-dd') + ' 00:00:00'
      }
      if (this.tabsName === 'QB') {
        reqData['chuanRanBingFL'] = 'all'
      } else if (this.tabsName === 'PT') {
        reqData['chuanRanBingFL'] = 'normol'
      } else if (this.tabsName === 'XB') {
        reqData['chuanRanBingFL'] = 'aids'
      } else if (this.tabsName === 'MBZ') {
        reqData['chuanRanBingFL'] = 'afp'
      } else if (this.tabsName === 'SZKB') {
        reqData['chuanRanBingFL'] = 'hfmd'
      }
      reqData['bingZhong'] = this.icd[this.tabsName]
      const res = await queryInfectiousDiseaseReport(reqData)
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
        this.tableData[this.tabsName] = res.data.jiLuLB
        this.total[this.tabsName] = res.data.jiLuZS
      }
    },
    async view(index, row) {
      let res = await viewInfectiousDiseaseReport({
        baoGaoID: row.cardid
      })
      this.dialogVisible = 'XX'
      this.cardData = res.data
    },
    onDel(index, row) {
      console.log(index, row)
      this.$confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await deleteReport({
            cardID: row.cardid,
            reportCardType: 'CRBBG'
          })
          if (res.hasError === -1) {
            this.$message.error(res.errorMessage)
          } else {
            this.$message({
              message: res.errorMessage,
              type: 'success'
            })
            await this.query()
          }
        })
        .catch(() => {})
    },
    setDZ() {
      this.dialogVisible = 'DZ'
      this.dzData = { leiBie: '0' }
    },
    handleChildData(data) {
      console.log(data)
      this.dialogVisible = 'LIST'
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  .dz {
    ::v-deep .el-select {
      width: 160px;
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  ::v-deep .el-tabs__nav {
    background: #ffffff;

    .is-active {
      background: #eaf0f9;
      border-bottom: none;
    }
  }

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  .content-table {
    flex: 1;
    min-height: 0;
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 150px;
    min-width: 150px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-scrollbar {
  max-width: 1400px !important;
}

::v-deep .el-tabs {
  display: flex;
  flex-direction: column;

  .el-tabs__content {
    flex: 1;
    min-height: 0;

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
