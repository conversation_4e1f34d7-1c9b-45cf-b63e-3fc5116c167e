<template>
  <div style="width: 100%; height: 100%; background-color: #ffffff">
    <div style="width: 1680px; margin: 0 auto; height: 100%">
      <div v-if="dialogVisible === 'LIST'" class="container">
        <div class="content">
          <el-tabs v-model="tabsName" type="card" style="height: 100%" @tab-click="tabsClick">
            <el-tab-pane label="肿瘤病例管理" name="ZL">
              <div class="header" style="border: 2px solid #ddd; margin-top: 12px">
                <div>
                  <div style="display: flex; align-items: center">
                    <div class="query-word">院区选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.yuanQuDM">
                        <el-radio label="55">龙港</el-radio>
                        <el-radio label="1">非龙港</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.huanZheLX">
                        <el-radio label="2">住院报告</el-radio>
                        <el-radio label="1">门诊报告</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告日期：</div>
                    <div class="query-value">
                      <el-date-picker
                        v-model="ruleForm.date"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                      ></el-date-picker>
                    </div>
                    <div class="query-word">审核状态：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.shenHeBZ">
                        <el-radio label="2">已审核</el-radio>
                        <el-radio label="1">未审核</el-radio>
                        <el-radio label="3">重卡</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">上传状态：</div>
                    <div class="query-value">
                      <el-select v-model="ruleForm.shangChuanZT">
                        <el-option label="全部" value="-1"></el-option>
                        <el-option label="已上传" value="1"></el-option>
                        <el-option label="未上传" value="2"></el-option>
                        <el-option label="上传失败" value="3"></el-option>
                      </el-select>
                    </div>
                    <div class="query-word">ICD编码：</div>
                    <div class="query-value">
                      <el-select v-model="icd['ZL']" clearable :popper-append-to-body="false">
                        <el-option
                          v-for="data in baseInfo['ZL'].zhenDuanBW"
                          :key="data.xianShiMC"
                          :label="data.xianShiMC"
                          :value="data.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                  </div>
                  <div style="display: flex; align-items: center; margin-top: 4px">
                    <div class="query-word">户籍选择：</div>
                    <div class="query-value dz">
                      <el-select
                        v-model="ruleForm.huJiSF"
                        placeholder="-省份-"
                        clearable
                        @change="setAddress($event, 1, 'huJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['huJi'].SF"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="query-value dz">
                      <el-select
                        v-model="ruleForm.huJiSJ"
                        placeholder="-市级-"
                        @change="setAddress($event, 2, 'huJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['huJi'].SJ"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="query-value dz">
                      <el-select
                        v-model="ruleForm.huJiQX"
                        placeholder="-区县-"
                        @change="setAddress($event, 3, 'huJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['huJi'].QX"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="query-value dz">
                      <el-select v-model="ruleForm.huJiJD" placeholder="-街道-">
                        <el-option
                          v-for="item in addressMapList['huJi'].JD"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="button">
                      <el-button type="primary" @click="query">查询</el-button>
                    </div>
                    <el-button type="primary" @click="setDZ">患者地址管理</el-button>
                  </div>
                </div>
              </div>
              <div class="content" style="padding: 0">
                <div class="content-header">
                  <div class="title">肿瘤报告卡管理</div>
                  <!--              <div>-->
                  <!--                <el-select v-model="ruleForm.zhengjianLX">-->
                  <!--                  <el-option label="按日期导出" value="1"></el-option>-->
                  <!--                  <el-option label="按编号导出" value="2"></el-option>-->
                  <!--                </el-select>-->
                  <!--                <el-button type="primary" @click="submitForm('ruleForm')">导出数据</el-button>-->
                  <!--              </div>-->
                </div>
                <div class="content-table">
                  <el-table
                    :data="tableData['ZL']"
                    border
                    size="medium"
                    height="100%"
                    style="width: 100%"
                  >
                    <el-table-column prop="bianHao" label="编号"></el-table-column>
                    <el-table-column prop="xingMing" label="姓名"></el-table-column>
                    <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
                    <el-table-column prop="nianLing" label="年龄"></el-table-column>
                    <el-table-column prop="huJiXXDZ" label="户籍"></el-table-column>
                    <el-table-column prop="bingLiHao" label="病理号"></el-table-column>
                    <el-table-column prop="bingLiLX" label="病理类型"></el-table-column>
                    <el-table-column prop="icd" label="ICD编码" width="100"></el-table-column>
                    <el-table-column prop="zhenDuanRQ" label="诊断日期">
                      <template slot-scope="scope">
                        {{ scope.row.zhenDuanRQ === null ? '' : scope.row.zhenDuanRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="baoGaoRQ" label="报告日期">
                      <template slot-scope="scope">
                        {{ scope.row.baoGaoRQ === null ? '' : scope.row.baoGaoRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="shenHeBZ" label="状态"></el-table-column>
                    <el-table-column prop="shangChuanBZ" label="上传"></el-table-column>
                    <el-table-column fixed="right" label="操作" align="center" width="160">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="view(scope.$index, scope.row)">
                          查看
                        </el-button>
                        <el-divider direction="vertical"></el-divider>
                        <el-button type="text" size="mini" @click="onDel(scope.$index, scope.row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-pagination
                  :current-page.sync="pageIndex['ZL']"
                  :page-size="pageSize"
                  layout="total, prev, pager, next"
                  :total="total['ZL']"
                  @current-change="setPage"
                ></el-pagination>
              </div>
            </el-tab-pane>
            <el-tab-pane label="糖尿病病例管理" name="TNB">
              <div class="header" style="border: 2px solid #ddd; margin-top: 12px">
                <div>
                  <div style="display: flex; align-items: center">
                    <div class="query-word">院区选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.yuanQuDM">
                        <el-radio label="55">龙港</el-radio>
                        <el-radio label="1">非龙港</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.huanZheLX">
                        <el-radio label="2">住院报告</el-radio>
                        <el-radio label="1">门诊报告</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告日期：</div>
                    <div class="query-value">
                      <el-date-picker
                        v-model="ruleForm.date"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                      ></el-date-picker>
                    </div>
                    <div class="query-word">审核状态：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.shenHeBZ">
                        <el-radio label="2">已审核</el-radio>
                        <el-radio label="1">未审核</el-radio>
                        <el-radio label="3">重卡</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">上传状态：</div>
                    <div class="query-value">
                      <el-select v-model="ruleForm.shangChuanZT">
                        <el-option label="全部" value="-1"></el-option>
                        <el-option label="已上传" value="1"></el-option>
                        <el-option label="未上传" value="2"></el-option>
                        <el-option label="上传失败" value="3"></el-option>
                      </el-select>
                    </div>
                    <div class="query-word">ICD编码：</div>
                    <div class="query-value">
                      <el-select v-model="icd['TNB']" clearable :popper-append-to-body="false">
                        <el-option
                          v-for="data in baseInfo['TNB'].tangNiaoBingZD"
                          :key="data.xianShiMC"
                          :label="data.xianShiMC"
                          :value="data.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                  </div>
                  <div style="display: flex; align-items: center; margin-top: 4px">
                    <div class="query-word">户籍选择：</div>
                    <div class="query-value dz">
                      <el-select
                        v-model="ruleForm.huJiSF"
                        placeholder="-省份-"
                        clearable
                        @change="setAddress($event, 1, 'huJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['huJi'].SF"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="query-value dz">
                      <el-select
                        v-model="ruleForm.huJiSJ"
                        placeholder="-市级-"
                        @change="setAddress($event, 2, 'huJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['huJi'].SJ"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="query-value dz">
                      <el-select
                        v-model="ruleForm.huJiQX"
                        placeholder="-区县-"
                        @change="setAddress($event, 3, 'huJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['huJi'].QX"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="query-value dz">
                      <el-select v-model="ruleForm.huJiJD" placeholder="-街道-">
                        <el-option
                          v-for="item in addressMapList['huJi'].JD"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="button">
                      <el-button type="primary" @click="query">查询</el-button>
                    </div>
                    <el-button type="primary" @click="setDZ">患者地址管理</el-button>
                  </div>
                </div>
              </div>
              <div class="content" style="padding: 0">
                <div class="content-header">
                  <div class="title">糖尿病报告卡管理</div>
                  <!--              <div>-->
                  <!--                <el-select v-model="ruleForm.zhengjianLX">-->
                  <!--                  <el-option label="按日期导出" value="1"></el-option>-->
                  <!--                  <el-option label="按编号导出" value="2"></el-option>-->
                  <!--                </el-select>-->
                  <!--                <el-button type="primary" @click="submitForm('ruleForm')">导出数据</el-button>-->
                  <!--              </div>-->
                </div>
                <div class="content-table">
                  <el-table
                    :data="tableData['TNB']"
                    border
                    size="medium"
                    height="100%"
                    style="width: 100%"
                  >
                    <el-table-column prop="bianHao" label="编号"></el-table-column>
                    <el-table-column prop="xingMing" label="姓名"></el-table-column>
                    <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
                    <el-table-column prop="nianLing" label="年龄"></el-table-column>
                    <el-table-column prop="huJiXXDZ" label="户籍"></el-table-column>
                    <el-table-column prop="bingLiHao" label="病理号"></el-table-column>
                    <el-table-column prop="bingLiLX" label="病理类型"></el-table-column>
                    <el-table-column prop="icd" label="ICD编码" width="100"></el-table-column>
                    <el-table-column prop="zhenDuanRQ" label="诊断日期">
                      <template slot-scope="scope">
                        {{ scope.row.zhenDuanRQ === null ? '' : scope.row.zhenDuanRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="baoGaoRQ" label="报告日期">
                      <template slot-scope="scope">
                        {{ scope.row.baoGaoRQ === null ? '' : scope.row.baoGaoRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="shenHeBZ" label="状态"></el-table-column>
                    <el-table-column prop="shangChuanBZ" label="上传"></el-table-column>
                    <el-table-column fixed="right" label="操作" align="center" width="160">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="view(scope.$index, scope.row)">
                          查看
                        </el-button>
                        <el-divider direction="vertical"></el-divider>
                        <el-button type="text" size="mini" @click="onDel(scope.$index, scope.row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-pagination
                  :current-page.sync="pageIndex['TNB']"
                  :page-size="pageSize"
                  layout="total, prev, pager, next"
                  :total="total['TNB']"
                  @current-change="setPage"
                ></el-pagination>
              </div>
            </el-tab-pane>
            <el-tab-pane label="心、脑血管疾病管理" name="XN">
              <div class="header" style="border: 2px solid #ddd; margin-top: 12px">
                <div>
                  <div style="display: flex; align-items: center">
                    <div class="query-word">院区选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.yuanQuDM">
                        <el-radio label="55">龙港</el-radio>
                        <el-radio label="1">非龙港</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告选择：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.huanZheLX">
                        <el-radio label="2">住院报告</el-radio>
                        <el-radio label="1">门诊报告</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">报告日期：</div>
                    <div class="query-value">
                      <el-date-picker
                        v-model="ruleForm.date"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                      ></el-date-picker>
                    </div>
                    <div class="query-word">审核状态：</div>
                    <div class="query-value">
                      <el-radio-group v-model="ruleForm.shenHeBZ">
                        <el-radio label="2">已审核</el-radio>
                        <el-radio label="1">未审核</el-radio>
                        <el-radio label="3">重卡</el-radio>
                      </el-radio-group>
                    </div>
                    <div class="query-word">上传状态：</div>
                    <div class="query-value">
                      <el-select v-model="ruleForm.shangChuanZT">
                        <el-option label="全部" value="-1"></el-option>
                        <el-option label="已上传" value="1"></el-option>
                        <el-option label="未上传" value="2"></el-option>
                        <el-option label="上传失败" value="3"></el-option>
                      </el-select>
                    </div>
                    <div class="query-word">ICD编码：</div>
                    <div class="query-value">
                      <el-select v-model="icd['XN']" clearable :popper-append-to-body="false">
                        <el-option
                          v-for="data in baseInfo['XN'].zhenDuan"
                          :key="data.xianShiMC"
                          :label="data.xianShiMC"
                          :value="data.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                  </div>
                  <div style="display: flex; align-items: center; margin-top: 4px">
                    <div class="query-word">户籍选择：</div>
                    <div class="query-value dz">
                      <el-select
                        v-model="ruleForm.huJiSF"
                        placeholder="-省份-"
                        clearable
                        @change="setAddress($event, 1, 'huJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['huJi'].SF"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="query-value dz">
                      <el-select
                        v-model="ruleForm.huJiSJ"
                        placeholder="-市级-"
                        @change="setAddress($event, 2, 'huJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['huJi'].SJ"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="query-value dz">
                      <el-select
                        v-model="ruleForm.huJiQX"
                        placeholder="-区县-"
                        @change="setAddress($event, 3, 'huJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['huJi'].QX"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="query-value dz">
                      <el-select v-model="ruleForm.huJiJD" placeholder="-街道-">
                        <el-option
                          v-for="item in addressMapList['huJi'].JD"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                    <div class="button">
                      <el-button type="primary" @click="query">查询</el-button>
                    </div>
                    <el-button type="primary" @click="setDZ">患者地址管理</el-button>
                  </div>
                </div>
              </div>
              <div class="content" style="padding: 0">
                <div class="content-header">
                  <div class="title">心脑报告卡管理</div>
                  <!--              <div>-->
                  <!--                <el-select v-model="ruleForm.zhengjianLX">-->
                  <!--                  <el-option label="按日期导出" value="1"></el-option>-->
                  <!--                  <el-option label="按编号导出" value="2"></el-option>-->
                  <!--                </el-select>-->
                  <!--                <el-button type="primary" @click="submitForm('ruleForm')">导出数据</el-button>-->
                  <!--              </div>-->
                </div>
                <div class="content-table">
                  <el-table
                    :data="tableData['XN']"
                    border
                    size="medium"
                    height="100%"
                    style="width: 100%"
                  >
                    <el-table-column prop="bianHao" label="编号"></el-table-column>
                    <el-table-column prop="xingMing" label="姓名"></el-table-column>
                    <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
                    <el-table-column prop="nianLing" label="年龄"></el-table-column>
                    <el-table-column prop="huJiXXDZ" label="户籍"></el-table-column>
                    <el-table-column prop="bingLiHao" label="病理号"></el-table-column>
                    <el-table-column prop="bingLiLX" label="病理类型"></el-table-column>
                    <el-table-column prop="icd" label="ICD编码" width="100"></el-table-column>
                    <el-table-column prop="zhenDuanRQ" label="诊断日期">
                      <template slot-scope="scope">
                        {{ scope.row.zhenDuanRQ === null ? '' : scope.row.zhenDuanRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="baoGaoRQ" label="报告日期">
                      <template slot-scope="scope">
                        {{ scope.row.baoGaoRQ === null ? '' : scope.row.baoGaoRQ.slice(0, 10) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="shenHeBZ" label="状态"></el-table-column>
                    <el-table-column prop="shangChuanBZ" label="上传"></el-table-column>
                    <el-table-column fixed="right" label="操作" align="center" width="160">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="view(scope.$index, scope.row)">
                          查看
                        </el-button>
                        <el-divider direction="vertical"></el-divider>
                        <el-button type="text" size="mini" @click="onDel(scope.$index, scope.row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-pagination
                  :current-page.sync="pageIndex['XN']"
                  :page-size="pageSize"
                  layout="total, prev, pager, next"
                  :total="total['XN']"
                  @current-change="setPage"
                ></el-pagination>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div v-else-if="dialogVisible === 'XX'" style="height: 100%">
        <tumor-report-card
          v-if="tabsName === 'ZL'"
          :patient="cardData"
          @childEvent="handleChildData"
        ></tumor-report-card>
        <diabetes-report-card
          v-else-if="tabsName === 'TNB'"
          :patient="cardData"
          @childEvent="handleChildData"
        ></diabetes-report-card>
        <heart-brain-report-card
          v-else-if="tabsName === 'XN'"
          :patient="cardData"
          @childEvent="handleChildData"
        ></heart-brain-report-card>
      </div>
      <div v-else-if="dialogVisible === 'DZ'" style="height: 100%">
        <address-management :patient="dzData" @childEvent="handleChildData"></address-management>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  deleteReport,
  getAddressListMxb,
  getBaseInfoTNB,
  getBaseInfoXN,
  getBaseInfoZL,
  queryIhronicDiseaseReport,
  viewTnbReport,
  viewXnReport,
  viewZlReport
} from '@/api/report-card'
import { format } from 'date-fns'
import TumorReportCard from '@/views/patient-inside/medical-related-report-card/TumorReportCard.vue'
import DiabetesReportCard from '@/views/patient-inside/medical-related-report-card/DiabetesReportCard.vue'
import HeartBrainReportCard from '@/views/patient-inside/medical-related-report-card/HeartBrainReportCard.vue'
import AddressManagement from '@/views/department-menus/report-card/address-management/index.vue'

export default {
  name: 'VerifyCheckOrdersStatus',
  components: {
    HeartBrainReportCard,
    // eslint-disable-next-line vue/no-unused-components
    TumorReportCard,
    DiabetesReportCard,
    AddressManagement
  },
  data() {
    return {
      tabsName: 'ZL',
      cardData: {},
      dzData: {},
      dialogVisible: 'LIST',
      ruleForm: {
        date: [],
        yuanQuDM: '1',
        huanZheLX: '2',
        icd: '',
        manXingBingFL: '', //慢性病分类
        huJiSF: '',
        huJiSJ: '',
        huJiQX: '',
        huJiJD: '',
        huJiDM: '', //户籍代码
        shangChuanZT: '-1', //上传状态
        shenHeBZ: '1', //审核标志
        // tangNiaoBingFL: '', //糖尿病分类
        kaiShiSJ: '', //开始时间
        jieShuSJ: '', //结束时间
        curr_page: 0, //当前页码
        count: 10 //每页数量
      },
      icd: {
        ZL: '',
        TNB: '',
        XN: ''
      },
      tableData: {
        ZL: [],
        TNB: [],
        XN: []
      },
      baseInfo: {
        ZL: {},
        TNB: {},
        XN: {}
      },
      total: {
        ZL: 0,
        TNB: 0,
        XN: 0
      },
      pageIndex: {
        ZL: 1,
        TNB: 1,
        XN: 1
      },
      pageSize: 10,
      addressMapList: {
        huJi: { SF: [], SJ: [], QX: [], JD: [] }
      },
      addressMap: { 0: 'SF', 1: 'SJ', 2: 'QX', 3: 'JD' }
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      const now = new Date()
      const date1 = new Date().setMonth(new Date().getMonth() - 1)
      const nowStr = format(now, 'yyyy-MM-dd')
      const date1Str = format(date1, 'yyyy-MM-dd')
      this.ruleForm['date'] = [date1Str, nowStr]
      await this.getReportCard()
      //获取地址信息
      this.addressMapList['huJi']['SF'] = await this.getAddress(0, 0)
    },
    async setPage(pageIndex) {
      this.pageIndex[this.tabsName] = pageIndex
      await this.query()
    },
    tabsClick(tab, event) {
      console.log(tab, event)
    },
    async getReportCard() {
      let res = {}
      res = await getBaseInfoZL({})
      this.baseInfo['ZL'] = res.data
      res = await getBaseInfoTNB({})
      this.baseInfo['TNB'] = res.data
      res = await getBaseInfoXN({})
      this.baseInfo['XN'] = res.data
    },
    async getAddress(id, code) {
      const res = await getAddressListMxb({ leiXing: id, shangJiDM: code })
      return res.data
    },
    async setAddress(code, id, type) {
      for (let i = id; i < 4; i++) {
        this.addressMapList[type][this.addressMap[i]] = []
        this.ruleForm[type + this.addressMap[i]] = ''
      }
      this.addressMapList[type][this.addressMap[id]] = await this.getAddress(id, code)
    },
    async query() {
      let reqData = JSON.parse(JSON.stringify(this.ruleForm))
      reqData['curr_page'] = this.pageIndex[this.tabsName]
      reqData['count'] = this.pageSize
      if (reqData.date) {
        reqData['kaiShiSJ'] = reqData.date[0] + ' 00:00:00'
        let jieShuSJ = new Date(reqData.date[1])
        jieShuSJ.setDate(jieShuSJ.getDate() + 1)
        reqData['jieShuSJ'] = format(jieShuSJ, 'yyyy-MM-dd') + ' 00:00:00'
      }
      for (const key in this.addressMap) {
        const k = this.addressMap[key]
        if (reqData['huJi' + k] !== '') {
          reqData['huJiDM'] += reqData['huJi' + k] + '-'
        }
      }
      if (reqData['huJiDM'] !== '') {
        reqData['huJiDM'] = reqData['huJiDM'].slice(0, -1)
      }
      if (this.tabsName === 'ZL') {
        reqData['manXingBingFL'] = 2
      } else if (this.tabsName === 'TNB') {
        reqData['manXingBingFL'] = 1
      } else if (this.tabsName === 'XN') {
        reqData['manXingBingFL'] = 3
      }
      reqData['icd'] = this.icd[this.tabsName]
      const res = await queryIhronicDiseaseReport(reqData)
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
        this.tableData[this.tabsName] = res.data.jiLuLB
        this.total[this.tabsName] = res.data.jiLuZS
      }
    },
    async view(index, row) {
      let res = ''
      if (this.tabsName === 'ZL') {
        res = await viewZlReport({
          baoGaoID: row.bianHao
        })
      } else if (this.tabsName === 'TNB') {
        res = await viewTnbReport({
          baoGaoID: row.bianHao
        })
      } else if (this.tabsName === 'XN') {
        res = await viewXnReport({
          baoGaoID: row.bianHao
        })
      }
      this.dialogVisible = 'XX'
      this.cardData = res.data
    },
    onDel(index, row) {
      this.$confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let reportCardType = ''
          if (this.tabsName === 'ZL') {
            reportCardType = 'ZLBG'
          } else if (this.tabsName === 'TNB') {
            reportCardType = 'TNBBG'
          } else if (this.tabsName === 'XN') {
            reportCardType = 'XNBG'
          }
          const res = await deleteReport({
            cardID: row.bianHao,
            reportCardType: reportCardType
          })
          if (res.hasError === -1) {
            this.$message.error(res.errorMessage)
          } else {
            this.$message({
              message: res.errorMessage,
              type: 'success'
            })
            await this.query()
          }
        })
        .catch(() => {})
    },
    setDZ() {
      this.dialogVisible = 'DZ'
      this.dzData = { leiBie: '1' }
    },
    handleChildData(data) {
      console.log(data)
      this.dialogVisible = 'LIST'
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  .dz {
    ::v-deep .el-select {
      width: 160px;
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  ::v-deep .el-tabs__nav {
    background: #ffffff;

    .is-active {
      background: #eaf0f9;
      border-bottom: none;
    }
  }

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  .content-table {
    flex: 1;
    min-height: 0;
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 150px;
    min-width: 150px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-scrollbar {
  max-width: 1400px !important;
}

::v-deep .el-tabs {
  display: flex;
  flex-direction: column;

  .el-tabs__content {
    flex: 1;
    min-height: 0;

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
