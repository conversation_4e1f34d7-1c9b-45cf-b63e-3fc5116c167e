<template>
  <div style="width: 100%; height: 100%; background-color: #ffffff">
    <div style="width: 1680px; margin: 0 auto; height: 100%">
      <div v-if="dialogVisible === 'LIST'" class="container">
        <div class="header">
          <div style="display: flex; align-items: center">
            <div class="query-word">查询条件：</div>
            <div class="query-value">
              <el-select v-model="ruleForm.lieBie" style="width: 120px">
                <el-option label="时间专科" value="1"></el-option>
                <el-option label="病案号" value="2"></el-option>
                <el-option label="住院号" value="3"></el-option>
                <el-option label="病人姓名" value="4"></el-option>
                <el-option label="门诊号" value="5"></el-option>
              </el-select>
            </div>
            <div v-if="ruleForm.lieBie === '1'" style="display: flex">
              <div class="query-value">
                <el-date-picker
                  v-model="ruleForm.date"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </div>
              <div class="query-value">
                <el-select v-model="ruleForm.zhuanKeID" style="width: 120px">
                  <el-option
                    v-for="item in zhuanKeList"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </div>
            </div>
            <div v-else class="query-value">
              <el-input v-model="ruleForm.canShu"></el-input>
            </div>
            <div class="button">
              <el-button type="primary" @click="query">查询</el-button>
              <el-button type="primary" @click="exportExcel">导出Excel</el-button>
            </div>
          </div>
        </div>
        <div class="content">
          <div class="content-header">
            <div class="title">死亡证明书记录</div>
          </div>
          <div class="content-table">
            <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
              <el-table-column prop="bianHao" label="编号"></el-table-column>
              <el-table-column prop="zhuYuanHao" label="病案号"></el-table-column>
              <el-table-column prop="bingRenXM" label="病人姓名"></el-table-column>
              <el-table-column prop="xingBie" label="性别"></el-table-column>
              <el-table-column prop="siWangSJ" label="死亡时间"></el-table-column>
              <el-table-column prop="siWangYYA" label="直接死亡的原因"></el-table-column>
              <el-table-column prop="huJiDZ" label="户籍地址"></el-table-column>
              <el-table-column prop="tianBiaoSJ" label="填写时间">
                <template slot-scope="scope">
                  {{ scope.row.tianBiaoSJ === null ? '' : scope.row.tianBiaoSJ.slice(0, 10) }}
                </template>
              </el-table-column>
              <el-table-column prop="zhuanKeMC" label="填写科室"></el-table-column>
              <el-table-column prop="yiShengQM" label="填写医生"></el-table-column>
              <el-table-column label="操作" align="center" width="160">
                <template slot-scope="scope">
                  <el-button type="text" size="mini" @click="view(scope.$index, scope.row)">
                    查看
                  </el-button>
                  <el-divider direction="vertical"></el-divider>
                  <el-button
                    type="text"
                    size="mini"
                    :disabled="scope.row.daYinZT === '2'"
                    @click="unseal(scope.$index, scope.row)"
                  >
                    解封
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-pagination
            :current-page.sync="pageIndex"
            :page-size="pageSize"
            layout="total, prev, pager, next"
            :total="total"
            @current-change="setPage"
          ></el-pagination>
        </div>
      </div>
      <div v-else-if="dialogVisible === 'XX'" style="height: 100%">
        <div class="container">
          <div class="content">
            <div class="content-header">
              <div class="title">居民死亡医学证明（推断）书</div>
              <div>
                <el-button type="primary" @click="sign">签字</el-button>
                <el-button type="primary" @click="upload">上传</el-button>
                <el-button type="primary" @click="print">打印</el-button>
                <el-button v-if="cardData.daYinZT === '2'" type="primary" @click="retransmission">
                  重传
                </el-button>
                <el-button type="primary" @click="sendData">返回上一层</el-button>
              </div>
            </div>
            <div class="content" style="border: #dadee5 1px solid; overflow: auto">
              <div class="el-form">
                <table>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>死者姓名：</span>
                    </td>
                    <td class="info-value">
                      <el-input
                        v-model="cardData.bingRenXM"
                        :disabled="cardData.daYinZT === '2'"
                      ></el-input>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>性别：</span>
                    </td>
                    <td class="info-value">
                      <el-input v-model="cardData.xingBie"></el-input>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>民族：</span>
                    </td>
                    <td class="info-value">
                      <el-select v-model="cardData.minZuDM">
                        <el-option
                          v-for="item in baseInfo.mingZuDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>国家或地区：</span>
                    </td>
                    <td class="info-value">
                      <el-select v-model="cardData.guoJiaDQDM">
                        <el-option
                          v-for="item in baseInfo.guoJiDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>有效身份证件类别：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-select
                        v-model="cardData.zhengJianLB"
                        :disabled="cardData.daYinZT === '2'"
                        style="width: 100%"
                      >
                        <el-option label="身份证" value="1"></el-option>
                        <el-option label="户口薄" value="2"></el-option>
                        <el-option label="护照" value="3"></el-option>
                        <el-option label="军官证" value="4"></el-option>
                        <el-option label="驾驶证" value="5"></el-option>
                        <el-option label="港澳通行证" value="6"></el-option>
                        <el-option label="台湾通行证" value="7"></el-option>
                        <el-option label="其他法定有效证件" value="9"></el-option>
                        <el-option label="无有效证件或不详" value="99"></el-option>
                      </el-select>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>证件号码：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-input
                        v-model="cardData.zhengJianHM"
                        :disabled="cardData.daYinZT === '2'"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>死者类型：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-select v-model="cardData.siZheLX" style="width: 100%">
                        <el-option label="普通" value="2"></el-option>
                        <el-option label="新生儿" value="1"></el-option>
                      </el-select>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>年龄：</span>
                    </td>
                    <td class="info-value">
                      <el-input v-model="cardData.siZheNL"></el-input>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>婚姻状况：</span>
                    </td>
                    <td class="info-value">
                      <el-select v-model="cardData.hunYinZT">
                        <el-option label="未婚" value="1"></el-option>
                        <el-option label="已婚" value="2"></el-option>
                        <el-option label="丧偶" value="3"></el-option>
                        <el-option label="离婚" value="4"></el-option>
                        <el-option label="未说明" value="9"></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>出生日期：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-date-picker
                        v-model="cardData.chuShengRQ"
                        value-format="yyyy-MM-dd"
                        type="date"
                        placeholder="选择日期"
                      ></el-date-picker>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>文化程度：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-select v-model="cardData.wenHuaCD" style="width: 100%">
                        <el-option label="研究生" value="1"></el-option>
                        <el-option label="大学" value="2"></el-option>
                        <el-option label="大专" value="3"></el-option>
                        <el-option label="中专" value="4"></el-option>
                        <el-option label="技校" value="5"></el-option>
                        <el-option label="高中" value="6"></el-option>
                        <el-option label="初中及以下" value="7"></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>个人身份：</span>
                    </td>
                    <td class="info-value" colspan="7">
                      <el-select v-model="cardData.zhiYeDM" style="width: 100%">
                        <el-option
                          v-for="item in baseInfo.zhiYeDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>死亡日期：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-date-picker
                        v-model="cardData.siWangSJ"
                        type="datetime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        placeholder="选择日期"
                      ></el-date-picker>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>死亡地点：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-select v-model="cardData.siWangDD" style="width: 100%">
                        <el-option label="医疗卫生机构" value="1"></el-option>
                        <el-option label="来院途中" value="2"></el-option>
                        <el-option label="家中" value="3"></el-option>
                        <el-option label="养老服务机构" value="4"></el-option>
                        <el-option label="其他场所" value="5"></el-option>
                        <el-option label="不详" value="0"></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>死亡时是否处于妊娠期或妊娠终止后42天内：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-select v-model="cardData.shiFouRS" style="width: 100%">
                        <el-option label="否" value="2"></el-option>
                        <el-option label="是" value="1"></el-option>
                      </el-select>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>生前工作单位：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-input v-model="cardData.shengQianGZDW"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>户籍地址：</span>
                    </td>
                    <td class="info-value" colspan="7">
                      <el-input v-model="cardData.huJiDZBMMC"></el-input>
                      <el-input v-model="cardData.huJiDZ"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>常住地址：</span>
                    </td>
                    <td class="info-value" colspan="7">
                      <el-input v-model="cardData.changZhuDZBMMC"></el-input>
                      <el-input v-model="cardData.changZhuDZ"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>被调查者姓名：</span>
                    </td>
                    <td class="info-value">
                      <el-input v-model="cardData.beiDiaoChaZXM"></el-input>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>被调查者联系电话：</span>
                    </td>
                    <td class="info-value">
                      <el-input v-model="cardData.beiDiaoChaZLXDH"></el-input>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>被调查者联系地址或工作单位：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-input v-model="cardData.beiDiaoChaRLXDZ"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>被调查者与死者关系：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-select v-model="cardData.siZheRWGX" style="width: 100%">
                        <el-option
                          v-for="item in baseInfo.lianXiRenGXDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>被调查者身份证号：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-input v-model="cardData.beiDiaoChaZSFZH"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>可联系的家属姓名：</span>
                    </td>
                    <td class="info-value">
                      <el-input v-model="cardData.lianXiJSXM"></el-input>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>联系电话：</span>
                    </td>
                    <td class="info-value">
                      <el-input v-model="cardData.jiaShuLXDH"></el-input>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>家属住址或工作单位：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-input v-model="cardData.jiaShuDZ"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>家属证件类型：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-select v-model="cardData.jiaShuYXSFZLB" style="width: 100%">
                        <el-option label="身份证" value="1"></el-option>
                        <el-option label="户口薄" value="2"></el-option>
                        <el-option label="护照" value="3"></el-option>
                        <el-option label="军官证" value="4"></el-option>
                        <el-option label="驾驶证" value="5"></el-option>
                        <el-option label="港澳通行证" value="6"></el-option>
                        <el-option label="台湾通行证" value="7"></el-option>
                        <el-option label="其他法定有效证件" value="9"></el-option>
                      </el-select>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>家属证件号码：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-input v-model="cardData.jiaShuZJHM"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>死因推断：</span>
                    </td>
                    <td class="info-value" colspan="7">
                      <el-input v-model="cardData.siYinTD"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>死亡医学证明根本死因：</span>
                    </td>
                    <td class="info-value" colspan="7">
                      <el-input v-model="cardData.siWangYXZMGBSY"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>死者生前病史及症状体症：</span>
                    </td>
                    <td class="info-value" colspan="7">
                      <el-input v-model="cardData.siZheSQBSJZZTZ"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>死亡详细地址：</span>
                    </td>
                    <td class="info-value" colspan="7">
                      <el-input v-model="cardData.siWangXXDZ"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label" colspan="2">
                      <span>致死的主要疾病诊断</span>
                    </td>
                    <td class="info-label" colspan="2">
                      <span>疾病名称</span>
                    </td>
                    <td class="info-label" colspan="2">
                      <span>发病至死亡大概间隔时间</span>
                    </td>
                    <td class="info-label" colspan="2">
                      <span>单位</span>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label" colspan="2">
                      <span class="red-star">*</span>
                      <span>Ⅰ.（a）直接死亡的原因：</span>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-input v-model="cardData.siWangYYA"></el-input>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-input v-model="cardData.jianGeSJA"></el-input>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-select v-model="cardData.jianGeSJADW" style="width: 100%">
                        <el-option value="2" label="小时"></el-option>
                        <el-option value="3" label="日"></el-option>
                        <el-option value="4" label="月"></el-option>
                        <el-option value="5" label="年"></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label" colspan="2">
                      <span class="red-star">*</span>
                      <span>（b）引起（a）的疾病或情况：</span>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-input v-model="cardData.siWangYYB"></el-input>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-input v-model="cardData.jianGeSJB"></el-input>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-select v-model="cardData.jianGeSJBDW" style="width: 100%">
                        <el-option value="2" label="小时"></el-option>
                        <el-option value="3" label="日"></el-option>
                        <el-option value="4" label="月"></el-option>
                        <el-option value="5" label="年"></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label" colspan="2">
                      <span class="red-star">*</span>
                      <span>（c）引起（b）的疾病或情况：</span>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-input v-model="cardData.siWangYYC"></el-input>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-input v-model="cardData.jianGeSJC"></el-input>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-select v-model="cardData.jianGeSJCDW" style="width: 100%">
                        <el-option value="2" label="小时"></el-option>
                        <el-option value="3" label="日"></el-option>
                        <el-option value="4" label="月"></el-option>
                        <el-option value="5" label="年"></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label" colspan="2">
                      <span class="red-star">*</span>
                      <span>（d）引起（c）的疾病或情况：</span>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-input v-model="cardData.siWangYYD"></el-input>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-input v-model="cardData.jianGeSJD"></el-input>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-select v-model="cardData.jianGeSJDDW" style="width: 100%">
                        <el-option value="2" label="小时"></el-option>
                        <el-option value="3" label="日"></el-option>
                        <el-option value="4" label="月"></el-option>
                        <el-option value="5" label="年"></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label" rowspan="3" colspan="2">
                      <span class="red-star">*</span>
                      <span>Ⅱ。其他疾病诊断（促进死亡，但与导致死亡无关的其他重要情况）：</span>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-input v-model="cardData.qiTaJBZDDJBMCA"></el-input>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-input v-model="cardData.qiTaJBZDDFBDSWDJGSJA"></el-input>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-select v-model="cardData.qiTaJBZDDDWA" style="width: 100%">
                        <el-option value="2" label="小时"></el-option>
                        <el-option value="3" label="日"></el-option>
                        <el-option value="4" label="月"></el-option>
                        <el-option value="5" label="年"></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-value" colspan="2">
                      <el-input v-model="cardData.qiTaJBZDDJBMCB"></el-input>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-input v-model="cardData.qiTaJBZDDFBDSWDJGSJB"></el-input>
                    </td>
                    <td class="info-value" colspan="2">
                      <el-select v-model="cardData.qiTaJBZDDDWB" style="width: 100%">
                        <el-option value="2" label="小时"></el-option>
                        <el-option value="3" label="日"></el-option>
                        <el-option value="4" label="月"></el-option>
                        <el-option value="5" label="年"></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>生前主要疾病最高诊断单位：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-select v-model="cardData.zuiGaoZDDW" style="width: 100%">
                        <el-option value="1" label="三级医院"></el-option>
                        <el-option value="2" label="二级医院"></el-option>
                        <el-option value="3" label="乡镇卫生院/社区、卫生服务机构"></el-option>
                        <el-option value="4" label="村卫生室"></el-option>
                        <el-option value="9" label="其他医疗卫生机构"></el-option>
                        <el-option value="0" label="未就诊"></el-option>
                      </el-select>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>死亡类型：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-select v-model="cardData.siWangLX">
                        <el-option value="1" label="医院死亡"></el-option>
                        <el-option value="0" label="非正常死亡"></el-option>
                        <el-option value="2" label="居家死亡"></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>生前主要疾病最高诊断依据：</span>
                    </td>
                    <td class="info-value" colspan="7">
                      <el-select v-model="cardData.zuiGaoZDYJ" style="width: 100%">
                        <el-option value="4" label="临床+理化"></el-option>
                        <el-option value="1" label="尸检"></el-option>
                        <el-option value="2" label="病理"></el-option>
                        <el-option value="3" label="手术"></el-option>
                        <el-option value="5" label="临床"></el-option>
                        <el-option value="6" label="死后推断"></el-option>
                        <el-option value="9" label="不详"></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>医师签名：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-input v-model="cardData.yiShengQM"></el-input>
                    </td>
                    <td class="info-label">
                      <span class="red-star">*</span>
                      <span>填表日期：</span>
                    </td>
                    <td class="info-value" colspan="3">
                      <el-date-picker
                        v-model="cardData.tianBiaoSJ"
                        value-format="yyyy-MM-dd"
                        type="date"
                        placeholder="选择日期"
                      ></el-date-picker>
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

import {
  getSiWangZMByCanShu,
  getWenShuListByGsdm,
  getZhuanKeList,
  initSiWangZMByBingLiID,
  reuploadSiWangZM,
  setSiWangZMZT,
  upSiWangZM
} from '@/api/report-card'
import { format } from 'date-fns'
import * as XLSX from 'xlsx'

import InfectiousDiseaseReportCard from '@/views/patient-inside/medical-related-report-card/InfectiousDiseaseReportCard.vue'
import { getGeShiDMFromRedis } from '@/api/progress-note'

export default {
  name: 'VerifyCheckOrdersStatus',
  components: {
    // eslint-disable-next-line vue/no-unused-components
    InfectiousDiseaseReportCard
  },
  data() {
    return {
      cardData: {},
      dialogVisible: 'LIST',
      ruleForm: {
        date: [],
        canShu: '',
        lieBie: '1',
        jieShuSJ: '',
        kaiShiSJ: '',
        zhuanKeID: ''
      },
      tableData: [],
      zhuanKeList: [],
      baseInfo: {},
      total: 0,
      pageIndex: 1,
      pageSize: 10,
      // 基础URL
      baseUrl: 'http://10.41.220.39/ehr'
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      const now = new Date()
      const date1 = new Date().setMonth(new Date().getMonth() - 1)
      const nowStr = format(now, 'yyyy-MM-dd')
      const date1Str = format(date1, 'yyyy-MM-dd')
      this.ruleForm['date'] = [date1Str, nowStr]
      await this.getReportCard()
    },
    async getReportCard() {
      let res = {}
      res = await getZhuanKeList({})
      this.zhuanKeList = res.data
      const res2 = await initSiWangZMByBingLiID({})
      this.baseInfo = res2.data
    },
    async setPage(pageIndex) {
      this.pageIndex = pageIndex
      await this.query()
    },
    async query() {
      let reqData = JSON.parse(JSON.stringify(this.ruleForm))
      reqData['curr_page'] = this.pageIndex
      reqData['count'] = this.pageSize
      if (reqData.date) {
        reqData['kaiShiSJ'] = reqData.date[0] + ' 00:00:00'
        let jieShuSJ = new Date(reqData.date[1])
        jieShuSJ.setDate(jieShuSJ.getDate() + 1)
        reqData['jieShuSJ'] = format(jieShuSJ, 'yyyy-MM-dd') + ' 00:00:00'
      }
      console.log(reqData)
      const res = await getSiWangZMByCanShu(reqData)
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
        this.tableData = res.data
        // this.total = this.tableData.length
      }
    },
    async view(index, row) {
      console.log(row)
      this.cardData = row
      this.dialogVisible = 'XX'
    },
    handleChildData(data) {
      console.log(data)
      this.dialogVisible = 'LIST'
    },
    async unseal(index, row) {
      const res = await setSiWangZMZT({
        jiLuID: row['jiLuID'],
        zhuangTai: '2'
      })
      console.log(res)
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
      }
    },
    async sign() {
      console.log('签字')
      const wenShuList = await getWenShuListByGsdm({
        bingLiID: this.cardData['bingLiID'],
        geShiDM: '10055'
      })
      console.log(wenShuList)
      if (wenShuList.data.length > 0) {
        console.log('存在')
      } else {
        console.log('不存在')
        //如果不存在数据则新增死亡记录文书
        const redis = await getGeShiDMFromRedis({ daiMa: '0265' })
        console.log(redis)
        const url = this.getRecordEditUrl(redis.data)
        console.log(url)
        window.open(url, '_blank', 'width=600,height=400,left=100,top=100')
      }
    },
    // 获取文书URL
    getRecordEditUrl(record) {
      const bingLiID = this.cardData['bingLiID']
      // 构建URL参数
      const params = {
        as_blid: bingLiID,
        as_gsdm: record.geShiDM,
        as_zyid: bingLiID,
        as_yhid: this.patientInfo.yiShiYHID,
        as_wsid: 0, // 新文书ID为0
        as_wslx: record.wenShuLX,
        as_tmpid: 't1',
        tmpid: Math.random()
      }
      // 将参数转换为URL查询字符串
      const queryString = Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join('&')
      // 返回完整URL
      return `${this.baseUrl}/zyblws/blwsdetail.aspx?${queryString}`
    },
    print() {
      if (this.cardData['diYiLianURL']) {
        window.open(
          this.cardData['diYiLianURL'],
          '_blank',
          'width=1200,height=800,left=100,top=100'
        )
      } else {
        this.$message.error('请先进行上传')
      }
    },
    async upload() {
      await upSiWangZM({ jiLuID: this.cardData['jiLuID'] })
    },
    async retransmission() {
      await reuploadSiWangZM({ jiLuID: this.cardData['jiLuID'] })
    },
    sendData() {
      this.dialogVisible = 'LIST'
    },
    exportExcel() {
      if (this.tableData.length === 0) {
        return this.$message.error('暂无数据可导出')
      }
      const keyMap = {
        bianHao: '编号',
        zhuYuanHao: '病案号',
        bingRenXM: '病人姓名',
        xingBie: '性别',
        siWangSJ: '死亡时间',
        siWangYYA: '直接死亡的原因',
        huJiDZ: '户籍地址',
        tianBiaoSJ: '填写时间',
        zhuanKeMC: '填写科室',
        yiShengQM: '填写医生'
      }
      // 创建工作表
      const worksheet = XLSX.utils.json_to_sheet(
        this.tableData.map((data) => {
          let d = {}
          for (const key in keyMap) {
            d[keyMap[key]] = data[key]
          }
          return d
        })
      )
      // 创建工作簿并添加工作表
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
      // 生成 Excel 文件并下载
      try {
        XLSX.writeFile(workbook, '死亡证明书查询.xlsx')
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  .dz {
    ::v-deep .el-select {
      width: 160px;
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  ::v-deep .el-tabs__nav {
    background: #ffffff;

    .is-active {
      background: #eaf0f9;
      border-bottom: none;
    }
  }

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  .content-table {
    flex: 1;
    min-height: 0;
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 200px;
    min-width: 200px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

.red-star {
  color: red;
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-scrollbar {
  max-width: 1400px !important;
}

::v-deep .el-tabs {
  display: flex;
  flex-direction: column;

  .el-tabs__content {
    flex: 1;
    min-height: 0;

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
