<template>
  <div class="container">
    <div class="header" style="position: relative">
      <div class="query-word">关键词：</div>
      <div class="query-value">
        <el-input v-model="GL"></el-input>
      </div>
      <div class="button">
        <el-button type="primary" @click="query">查询</el-button>
        <el-button type="primary" @click="dialogFormVisible = true">新增</el-button>
      </div>
      <el-button
        v-if="patient"
        type="primary"
        style="position: absolute; right: 14px"
        @click="sendData"
      >
        返回上一层
      </el-button>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">地址管理</div>
      </div>
      <div class="content-table" style="position: relative">
        <el-table :data="tableData" border size="mini" style="width: 100%">
          <el-table-column prop="id" label="编号"></el-table-column>
          <el-table-column prop="guanJianCi" label="关键字"></el-table-column>
          <el-table-column label="省">
            <template slot-scope="scope">
              {{ scope.row.diZhiWZXX.split('-')[0] }}
            </template>
          </el-table-column>
          <el-table-column label="市">
            <template slot-scope="scope">
              {{ scope.row.diZhiWZXX.split('-')[1] }}
            </template>
          </el-table-column>
          <el-table-column label="区县">
            <template slot-scope="scope">
              {{ scope.row.diZhiWZXX.split('-')[2] }}
            </template>
          </el-table-column>
          <el-table-column label="街道">
            <template slot-scope="scope">
              {{ scope.row.diZhiWZXX.split('-')[3] }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="160">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="onUpdate(scope.$index, scope.row)">
                修改
              </el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button type="text" size="mini" @click="onDelete(scope.$index, scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          style="position: absolute; bottom: 0; right: 0"
          :current-page.sync="pageIndex"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="total"
          @current-change="setPage"
        ></el-pagination>
      </div>
    </div>
    <el-dialog :title="upTitle" :visible.sync="dialogFormVisible" :before-close="onClose">
      <el-form :model="ruleForm">
        <el-form-item label="关键词：" :label-width="formLabelWidth">
          <el-input v-model="ruleForm.guanJianCi" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="详细地址：" class="cz-select" :label-width="formLabelWidth">
          <el-form-item prop="diZhiSF">
            <el-select
              v-model="ruleForm.diZhiSF"
              placeholder="-省份-"
              @change="setAddress($event, 1, 'diZhi')"
            >
              <el-option
                v-for="item in addressMapList['diZhi'].SF"
                :key="item.daiMa"
                :label="item.mingCheng"
                :value="item.daiMa"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="diZhiSJ">
            <el-select
              v-model="ruleForm.diZhiSJ"
              placeholder="-市级-"
              @change="setAddress($event, 2, 'diZhi')"
            >
              <el-option
                v-for="item in addressMapList['diZhi'].SJ"
                :key="item.daiMa"
                :label="item.mingCheng"
                :value="item.daiMa"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="diZhiQX">
            <el-select
              v-model="ruleForm.diZhiQX"
              placeholder="-区县-"
              @change="setAddress($event, 3, 'diZhi')"
            >
              <el-option
                v-for="item in addressMapList['diZhi'].QX"
                :key="item.daiMa"
                :label="item.mingCheng"
                :value="item.daiMa"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="diZhiJD">
            <el-select v-model="ruleForm.diZhiJD" placeholder="-街道-">
              <el-option
                v-for="item in addressMapList['diZhi'].JD"
                :key="item.daiMa"
                :label="item.mingCheng"
                :value="item.daiMa"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button v-if="update === true" type="primary" @click="onSave(true)">修改</el-button>
        <el-button v-else type="primary" @click="onSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  addYlGwjkSdtbhzdzPo,
  deleteYlGwjkSdtbhzdzPo,
  getAddressList,
  getAddressListMxbModify,
  getYlGwjkSdtbhzdzPoList,
  updateYlGwjkSdtbhzdzPo
} from '@/api/report-card'

export default {
  name: 'AddressManagement',
  props: {
    patient: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageIndex: 1,
      pageSize: 14,
      total: 0,
      leiBie: '1',
      GL: '',
      tableData: [],
      dialogFormVisible: false,
      formLabelWidth: '120px',
      ruleForm: {
        guanJianCi: '',
        leiBie: '',
        diZhiSF: '',
        diZhiSJ: '',
        diZhiQX: '',
        diZhiJD: ''
      },
      addressMapList: {
        diZhi: { SF: [], SJ: [], QX: [], JD: [] }
      },
      addressMap: { 0: 'SF', 1: 'SJ', 2: 'QX', 3: 'JD' },
      update: false,
      upTitle: '新增'
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      this.leiBie = this.patient.leiBie
      await this.query()
      //获取地址信息
      this.addressMapList['diZhi']['SF'] = await this.getAddress(0, 0)
    },
    async query() {
      this.pageIndex = 1
      const res = await getYlGwjkSdtbhzdzPoList({
        leiBie: this.leiBie,
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
        key: this.GL
      })
      this.tableData = res.data.records
      this.total = res.data.total
    },
    async setPage(pageIndex) {
      this.pageIndex = pageIndex
      const res = await getYlGwjkSdtbhzdzPoList({
        leiBie: this.leiBie,
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
        key: this.GL
      })
      this.tableData = res.data.records
    },
    async getAddress(id, code) {
      let res = {}
      if (this.leiBie === '1') {
        res = await getAddressListMxbModify({ leiXing: id, shangJiDM: code })
      } else {
        res = await getAddressList({ leiXing: id, shangJiDM: code })
      }
      console.log(res)
      return res.data
    },
    async setAddress(code, id, type) {
      for (let i = id; i < 4; i++) {
        this.addressMapList[type][this.addressMap[i]] = []
        this.ruleForm[type + this.addressMap[i]] = ''
      }
      this.addressMapList[type][this.addressMap[id]] = await this.getAddress(id, code)
    },
    sendData() {
      const data = { dialogVisible: false }
      this.$emit('childEvent', data) // 触发事件并传递数据
    },
    onClose() {
      this.dialogFormVisible = false
      this.ruleForm = {
        guanJianCi: '',
        leiBie: '',
        diZhiSF: '',
        diZhiSJ: '',
        diZhiQX: '',
        diZhiJD: ''
      }
      this.update = false
      this.upTitle = '新增'
    },
    onDelete(index, row) {
      this.$confirm('此操作将删除该地址, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await deleteYlGwjkSdtbhzdzPo({
            id: row.id
          })
          if (this.msg(res)) {
            await this.query()
          }
        })
        .catch(() => {})
    },
    async onUpdate(index, row) {
      this.upTitle = '修改'
      this.update = true
      this.dialogFormVisible = true
      this.ruleForm['guanJianCi'] = row.guanJianCi
      this.ruleForm['diZhiSF'] = row.diZhiWZBM.split('-')[0]
      await this.setAddress(this.ruleForm['diZhiSF'], 1, 'diZhi')
      this.ruleForm['diZhiSJ'] = row.diZhiWZBM.split('-')[1]
      await this.setAddress(this.ruleForm['diZhiSJ'], 2, 'diZhi')
      this.ruleForm['diZhiQX'] = row.diZhiWZBM.split('-')[2]
      await this.setAddress(this.ruleForm['diZhiQX'], 3, 'diZhi')
      this.ruleForm['diZhiJD'] = row.diZhiBM
      this.ruleForm['id'] = row.id
    },
    async onSave(update) {
      let data = { ...this.ruleForm }
      data['diZhiBM'] = data['diZhiJD']
      data['diZhiWZBM'] =
        data['diZhiSF'] + '-' + data['diZhiSJ'] + '-' + data['diZhiQX'] + '-' + data['diZhiJD']
      data['duiZhaoLB'] = this.leiBie
      data['leiBie'] = this.leiBie
      data['diZhiWZXX'] = ''
      for (let i = 0; i < 4; i++) {
        const k = this.addressMap[i]
        const key = 'diZhi' + this.addressMap[i]
        for (const d of this.addressMapList['diZhi'][k]) {
          if (d['daiMa'] === data[key]) {
            data['diZhiWZXX'] += d['mingCheng'] + '-'
            break
          }
        }
      }
      data['diZhiWZXX'] = data['diZhiWZXX'].slice(0, -1)
      let res = {}
      if (update) {
        res = await updateYlGwjkSdtbhzdzPo(data)
      } else {
        res = await addYlGwjkSdtbhzdzPo(data)
      }
      if (this.msg(res)) {
        this.onClose()
        await this.query()
      }
    },
    msg(res) {
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
        return false
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
        return true
      }
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;
    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  .dz {
    ::v-deep .el-select {
      width: 160px;
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  ::v-deep .el-tabs__nav {
    background: #ffffff;
    .is-active {
      background: #eaf0f9;
      border-bottom: none;
    }
  }

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  .content-table {
    flex: 1;
    min-height: 0;
  }

  table {
    width: 100%;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }
  .info-label {
    text-align: right;
    width: 150px;
    min-width: 150px;
    background-color: #eaf0f9;
  }
  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }
  .info-value {
    background-color: #f7f9fd;
    .value {
      margin-left: 10px;
      .risk {
        margin-right: 15px;
      }
    }
    a {
      text-decoration: underline;
      color: #356ac5;
    }
    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }
    .cz-select {
      display: flex;
      align-items: center;
      ::v-deep .el-form-item {
        width: auto;
      }
      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }
      ::v-deep .el-input {
        margin-right: 5px;
      }
    }
    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }
      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }
      ::v-deep .el-select {
        width: 100%;
      }
      ::v-deep .el-input {
        width: 100%;
      }
    }
    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }
    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }
      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-scrollbar {
  max-width: 1400px !important;
}

::v-deep .el-tabs {
  display: flex;
  flex-direction: column;
  .el-tabs__content {
    flex: 1;
    min-height: 0;
    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}

.cz-select {
  ::v-deep .el-select {
    width: 160px;
    margin-right: 10px;
  }

  ::v-deep .el-form-item__content {
    border: none;
  }
}
</style>
