<template>
  <div style="width: 100%; height: 100%; background-color: #ffffff">
    <div style="width: 1680px; margin: 0 auto; height: 100%">
      <div v-if="dialogVisible === 'LIST'" class="container">
        <div class="header">
          <div style="display: flex; align-items: center">
            <div class="query-word">报告日期：</div>
            <div class="query-value">
              <el-date-picker
                v-model="ruleForm.date"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </div>
            <div class="button">
              <el-button type="primary" @click="query">查询</el-button>
            </div>
          </div>
        </div>
        <div class="content">
          <div class="content-header">
            <div class="title">重精报告卡管理</div>
          </div>
          <div class="content-table">
            <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
              <el-table-column prop="xuHao" label="序号"></el-table-column>
              <el-table-column prop="xingMing" label="姓名"></el-table-column>
              <el-table-column prop="jiBingMC" label="疾病名称"></el-table-column>
              <el-table-column prop="tianKaRQ" label="填卡时间">
                <template slot-scope="scope">
                  {{ scope.row.tianKaRQ === null ? '' : scope.row.tianKaRQ.slice(0, 10) }}
                </template>
              </el-table-column>
              <el-table-column prop="queZhenRQ" label="就诊日期">
                <template slot-scope="scope">
                  {{ scope.row.queZhenRQ === null ? '' : scope.row.queZhenRQ.slice(0, 10) }}
                </template>
              </el-table-column>
              <el-table-column prop="lianXiDH" label="联系电话"></el-table-column>
              <el-table-column prop="zhuangTai" label="状态"></el-table-column>
              <el-table-column prop="shangChuanZT" label="是否上传"></el-table-column>
              <el-table-column label="操作" align="center" width="160">
                <template slot-scope="scope">
                  <el-button type="text" size="mini" @click="view(scope.$index, scope.row)">
                    查看
                  </el-button>
                  <el-divider direction="vertical"></el-divider>
                  <el-button type="text" size="mini" @click="onDel(scope.$index, scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-pagination
            :current-page.sync="pageIndex"
            :page-size="pageSize"
            layout="total, prev, pager, next"
            :total="total"
            @current-change="setPage"
          ></el-pagination>
        </div>
      </div>
      <div v-else-if="dialogVisible === 'XX'" style="height: 100%">
        <psychoses-report-card
          :patient="cardData"
          @childEvent="handleChildData"
        ></psychoses-report-card>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  deleteReport,
  seriousMentalDiseaseReport,
  viewInfectiousDiseaseReport,
  viewSeriousMentalDiseaseReport
} from '@/api/report-card'
import { format } from 'date-fns'
import PsychosesReportCard from '@/views/patient-inside/medical-related-report-card/PsychosesReportCard.vue'

export default {
  name: 'VerifyCheckOrdersStatus',
  components: {
    // eslint-disable-next-line vue/no-unused-components
    PsychosesReportCard
  },
  data() {
    return {
      cardData: {},
      dialogVisible: 'LIST',
      ruleForm: {
        kaiShiSJ: '', //开始时间
        jieShuSJ: '', //结束时间
        date: [],
        curr_page: 0, //当前页码
        count: 10 //每页数量
      },
      tableData: [],
      total: 0,
      pageIndex: 1,
      pageSize: 10
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      const now = new Date()
      const date1 = new Date().setMonth(new Date().getMonth() - 1)
      const nowStr = format(now, 'yyyy-MM-dd')
      const date1Str = format(date1, 'yyyy-MM-dd')
      this.ruleForm['date'] = [date1Str, nowStr]
    },
    async setPage(pageIndex) {
      this.pageIndex = pageIndex
      await this.query()
    },
    async query() {
      let reqData = JSON.parse(JSON.stringify(this.ruleForm))
      reqData['curr_page'] = this.pageIndex
      reqData['count'] = this.pageSize
      if (reqData.date) {
        reqData['kaiShiSJ'] = reqData.date[0] + ' 00:00:00'
        let jieShuSJ = new Date(reqData.date[1])
        jieShuSJ.setDate(jieShuSJ.getDate() + 1)
        reqData['jieShuSJ'] = format(jieShuSJ, 'yyyy-MM-dd') + ' 00:00:00'
      }
      console.log(reqData)
      const res = await seriousMentalDiseaseReport(reqData)
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
        this.tableData = res.data.jiLuLB
        this.total = res.data.jiLuZS
      }
    },
    async view(index, row) {
      let res = await viewSeriousMentalDiseaseReport({
        baoGaoZJ: row.id
      })
      this.dialogVisible = 'XX'
      this.cardData = res.data
    },
    onDel(index, row) {
      console.log(index, row)
      this.$confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await deleteReport({
            cardID: row.id,
            reportCardType: 'ZJBG'
          })
          if (res.hasError === -1) {
            this.$message.error(res.errorMessage)
          } else {
            this.$message({
              message: res.errorMessage,
              type: 'success'
            })
            await this.query()
          }
        })
        .catch(() => {})
    },
    handleChildData(data) {
      console.log(data)
      this.dialogVisible = 'LIST'
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  .dz {
    ::v-deep .el-select {
      width: 160px;
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  ::v-deep .el-tabs__nav {
    background: #ffffff;

    .is-active {
      background: #eaf0f9;
      border-bottom: none;
    }
  }

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  .content-table {
    flex: 1;
    min-height: 0;
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 150px;
    min-width: 150px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-scrollbar {
  max-width: 1400px !important;
}

::v-deep .el-tabs {
  display: flex;
  flex-direction: column;

  .el-tabs__content {
    flex: 1;
    min-height: 0;

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
