<template>
  <!-- 术后预防使用抗菌药物患者一览表 -->
  <div class="container">
    <!-- 数据展示区域 -->
    <div class="table-container">
      <div class="title">
        <span>术后预防使用抗菌药物患者一览表</span>
        <span class="total-count">共{{ tableData.length }}条数据</span>
      </div>
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 30px)"
        size="mini"
      >
        <el-table-column prop="bingAnHao" label="病案号" min-width="120"></el-table-column>
        <el-table-column prop="chuangWei" label="床位" min-width="80"></el-table-column>
        <el-table-column prop="xingMing" label="姓名" min-width="100"></el-table-column>
        <el-table-column prop="ypmc" label="药品名称" min-width="150"></el-table-column>
        <el-table-column prop="kssj" label="开始时间" min-width="150"></el-table-column>
        <el-table-column prop="jssj" label="结束时间" min-width="150"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getPostOpAntibioticPatients } from '@/api/sugery-related'
import { mapState } from 'vuex'

export default {
  name: 'PostoperativeAntibioticsPatients',
  data() {
    return {
      // 表格数据
      tableData: []
    }
  },
  computed: {
    ...mapState({
      initInfo: ({ patient }) => patient.initInfo
    })
  },
  created() {
    this.loadData()
  },
  methods: {
    // 加载数据
    async loadData() {
      try {
        const params = {}

        // 使用当前用户的专科ID
        if (this.initInfo?.zhuanKeID) {
          params.zhuanKeID = this.initInfo.zhuanKeID
        }

        const res = await getPostOpAntibioticPatients(params)

        if (res.hasError === 0) {
          this.tableData = res.data || []
        }
      } catch (error) {
        this.$message.error(error.errorMessage || '获取数据失败')
        this.tableData = []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  padding: 10px;
  background: #fff;
}

.table-container {
  height: 100%;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .total-count {
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
