<template>
  <!-- 术后预防使用抗菌药物患者一览表 -->
  <div class="container">
    <div class="filter-container">
      <div class="filter-col">
        <!-- 医生用户：专科选择 -->
        <div v-if="renYuanLB === '01'" class="filter-item">
          <span class="filter-label">选择专科：</span>
          <el-select
            v-model="selectedZhuanKe"
            placeholder="请选择专科"
            style="width: 200px"
            size="mini"
          >
            <el-option
              v-for="item in [{ buMenID: '0', buMenMC: '所有专科' }, ...zhuanKeList]"
              :key="item.buMenID"
              :label="item.buMenMC"
              :value="item.buMenID"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="filter-col">
        <div class="filter-item">
          <el-button type="primary" size="mini" class="purple-button" @click="handleQuery">
            查询
          </el-button>
          <el-button type="success" size="mini" @click="exportToExcel">导出Excel</el-button>
        </div>
      </div>
    </div>

    <!-- 数据展示区域 -->
    <div class="table-container">
      <div class="title">
        <span>术后预防使用抗菌药物患者一览表</span>
        <span class="total-count">共{{ tableData.length }}条数据</span>
      </div>
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 30px)"
        size="mini"
        v-loading="loading"
      >
        <el-table-column prop="bingAnHao" label="病案号" min-width="120"></el-table-column>
        <el-table-column prop="chuangWei" label="床位" min-width="80"></el-table-column>
        <el-table-column prop="xingMing" label="姓名" min-width="100"></el-table-column>
        <el-table-column prop="ypmc" label="药品名称" min-width="150"></el-table-column>
        <el-table-column prop="kssj" label="开始时间" min-width="150"></el-table-column>
        <el-table-column prop="jssj" label="结束时间" min-width="150"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getPostOpAntibioticPatients } from '@/api/sugery-related'
import { getZhuanKeList } from '@/api/medical-quality'
import { mapState } from 'vuex'
import { format } from 'date-fns'
import * as XLSX from 'xlsx'

export default {
  name: 'PostoperativeAntibioticsPatients',
  data() {
    return {
      // 查询参数
      selectedZhuanKe: '0',
      // 表格数据
      tableData: [],
      // 加载状态
      loading: false
    }
  },
  computed: {
    ...mapState({
      renYuanLB: ({ patient }) => patient.doctorInfo?.renYuanLB,
      zhuanKeList: ({ patient }) => patient.zhuanKeList,
      initInfo: ({ patient }) => patient.initInfo
    })
  },
  created() {
    this.init()
  },
  methods: {
    // 初始化
    async init() {
      // 获取专科列表
      await this.getZhuanKeList()

      if (this.renYuanLB === '01') {
        // 医生用户：设置默认专科
        this.selectedZhuanKe = '0'
      } else {
        // 其他用户：使用当前专科
        this.selectedZhuanKe = this.initInfo?.zhuanKeID || '0'
      }

      // 初始查询
      await this.handleQuery()
    },

    // 获取专科列表
    async getZhuanKeList() {
      try {
        if (!this.zhuanKeList || this.zhuanKeList.length === 0) {
          await this.$store.dispatch('patient/getZhuanKeList')
        }
      } catch (error) {
        console.error('获取专科列表失败:', error)
      }
    },

    // 查询数据
    async handleQuery() {
      try {
        this.loading = true

        const params = {}

        // 根据用户类型设置查询参数
        if (this.renYuanLB === '01') {
          // 医生用户：可以选择专科
          if (this.selectedZhuanKe !== '0') {
            params.zhuanKeID = this.selectedZhuanKe
          }
        } else {
          // 其他用户：使用当前专科
          params.zhuanKeID = this.initInfo?.zhuanKeID
        }

        const res = await getPostOpAntibioticPatients(params)

        this.loading = false

        if (res && res.hasError === 0 && res.data) {
          this.tableData = res.data || []
        } else {
          this.tableData = []
          this.$message.warning(res?.errorMessage || '暂无数据')
        }
      } catch (error) {
        this.loading = false
        this.$message.error(error.errorMessage || '查询数据失败')
        this.tableData = []
      }
    },

    // 导出Excel
    exportToExcel() {
      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      try {
        // 准备导出数据
        const exportData = this.tableData.map((item) => {
          return {
            病案号: item.bingAnHao || '',
            床位: item.chuangWei || '',
            姓名: item.xingMing || '',
            药品名称: item.ypmc || '',
            开始时间: item.kssj || '',
            结束时间: item.jssj || ''
          }
        })

        // 创建工作表
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

        // 生成文件名
        const fileName = `术后预防使用抗菌药物患者一览表_${format(new Date(), 'yyyy-MM-dd')}.xlsx`

        // 下载文件
        XLSX.writeFile(workbook, fileName)
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  padding: 10px;
  background: #fff;
}

.filter-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  display: flex;
}

.filter-col {
  display: flex;
  padding: 0 8px;

  .filter-item {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.filter-label {
  font-weight: bold;
  min-width: 80px;
}

.purple-button {
  background: #a66dd4;
  border: 1px solid #a66dd4;
  &:hover,
  &:focus {
    background: #ce8be0;
    border-color: #ce8be0;
  }
}

.table-container {
  height: calc(100% - 62px);
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .total-count {
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
