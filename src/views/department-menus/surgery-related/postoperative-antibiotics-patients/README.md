# 术后预防使用抗菌药物患者一览表

## 功能说明

本页面是一个简洁的只读列表页面，用于展示当前专科术后预防使用抗菌药物的患者数据。

## 主要功能

### 1. 数据展示
- **病案号**：患者的病案编号
- **床位**：患者所在床位
- **姓名**：患者姓名
- **药品名称**：使用的抗菌药物名称
- **开始时间**：用药开始时间
- **结束时间**：用药结束时间

### 2. 导出功能
- **Excel导出**：将当前数据导出为Excel文件
- **文件命名**：自动生成带日期的文件名

## 技术实现

### API接口
- **接口方法**：`getPostOpAntibioticPatients`
- **接口文件**：`@/api/sugery-related.js`
- **请求参数**：`zhuanKeID`（当前用户专科ID）

### 数据字段映射
```javascript
{
  bingAnHao: '病案号',
  chuang<PERSON>ei: '床位',
  xingMing: '姓名',
  ypmc: '药品名称',
  kssj: '开始时间',
  jssj: '结束时间'
}
```

### 数据获取逻辑
- 自动使用当前用户的专科ID（`initInfo?.zhuanKeID`）
- 页面初始化时自动加载数据
- 无需手动查询操作

## 页面特点

### 简化设计
- 移除了查询条件区域
- 移除了专科选择功能
- 移除了加载状态显示
- 直接展示当前专科数据

### 保留功能
- 完整的数据表格展示
- Excel导出功能
- 响应式表格布局
- 数据统计显示

## 使用说明

1. 用户进入页面后自动加载当前专科数据
2. 表格直接显示术后预防使用抗菌药物患者信息
3. 点击"导出Excel"按钮下载数据
4. 页面布局自适应不同屏幕尺寸

## 注意事项

- 确保用户已登录并有相应权限
- 数据基于当前用户的专科权限
- 导出功能需要浏览器支持文件下载
- 异常情况会显示相应的错误提示
