# 术后预防使用抗菌药物患者一览表

## 功能说明

本页面用于展示术后预防使用抗菌药物的患者列表，提供查询和导出功能。

## 主要功能

### 1. 数据展示
- **病案号**：患者的病案编号
- **床位**：患者所在床位
- **姓名**：患者姓名
- **药品名称**：使用的抗菌药物名称
- **开始时间**：用药开始时间
- **结束时间**：用药结束时间

### 2. 查询功能
- **专科选择**：医生用户可以选择不同专科进行查询
- **查询按钮**：点击查询获取最新数据

### 3. 导出功能
- **Excel导出**：将查询结果导出为Excel文件
- **文件命名**：自动生成带日期的文件名

## 技术实现

### API接口
- **接口方法**：`getPostOpAntibioticPatients`
- **接口文件**：`@/api/sugery-related.js`
- **请求参数**：`zhuanKeID`（专科ID）

### 数据字段映射
```javascript
{
  bingAnHao: '病案号',
  chuangWei: '床位', 
  xingMing: '姓名',
  ypmc: '药品名称',
  kssj: '开始时间',
  jssj: '结束时间'
}
```

### 用户权限
- **医生用户**（renYuanLB === '01'）：可选择专科查询
- **其他用户**：使用当前专科数据

## 样式设计

参考了 `admission-24h-screening-query` 页面的设计风格：
- 筛选区域使用浅蓝色背景
- 表格区域带有阴影效果
- 紫色主题按钮
- 响应式表格布局

## 使用说明

1. 页面加载时自动获取专科列表
2. 医生用户可以选择专科，其他用户使用默认专科
3. 点击"查询"按钮获取数据
4. 点击"导出Excel"按钮下载数据
5. 表格支持排序和筛选功能

## 注意事项

- 确保用户已登录并有相应权限
- 导出功能需要浏览器支持文件下载
- 数据加载时显示loading状态
- 异常情况会显示相应的错误提示
