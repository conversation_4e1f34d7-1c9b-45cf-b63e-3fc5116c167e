<template>
  <!-- 我的相关手术查询 -->
  <div class="container">
    <div class="filter-container">
      <div class="filter-col">
        <!-- 日期范围选择 -->
        <div class="filter-item">
          <span class="filter-label">手术日期：</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            size="mini"
            style="width: 240px"
          ></el-date-picker>
        </div>

        <!-- 病人姓名 -->
        <div class="filter-item">
          <span class="filter-label">病人姓名：</span>
          <el-input
            v-model="patientName"
            placeholder="请输入病人姓名"
            size="mini"
            style="width: 150px"
            clearable
          ></el-input>
        </div>
      </div>

      <div class="filter-col">
        <!-- 住院号 -->
        <div class="filter-item">
          <span class="filter-label">住院号：</span>
          <el-input
            v-model="admissionNumber"
            placeholder="请输入住院号"
            size="mini"
            style="width: 150px"
            clearable
          ></el-input>
        </div>

        <!-- 病人专科 -->
        <div class="filter-item">
          <span class="filter-label">病人专科：</span>
          <el-select
            v-model="selectedDepartment"
            placeholder="请选择专科"
            size="mini"
            style="width: 150px"
            clearable
          >
            <el-option
              v-for="item in [{ buMenID: '', buMenMC: '所有专科' }, ...departmentList]"
              :key="item.buMenID"
              :label="item.buMenMC"
              :value="item.buMenID"
            ></el-option>
          </el-select>
        </div>
      </div>

      <div class="filter-col">
        <div class="filter-item">
          <el-button type="primary" size="mini" class="purple-button" @click="handleQuery">
            查询
          </el-button>
          <el-button type="success" size="mini" @click="exportToExcel">导出Excel</el-button>
        </div>
      </div>
    </div>

    <!-- 数据展示区域 -->
    <div class="table-container">
      <div class="title">
        <span>我的相关手术查询</span>
        <span class="total-count">共{{ tableData.length }}条数据</span>
      </div>
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 30px)"
        size="mini"
        v-loading="loading"
      >
        <el-table-column prop="shouShuJianMC" label="手术间" min-width="80"></el-table-column>
        <el-table-column prop="zyh" label="住院号" min-width="100"></el-table-column>
        <el-table-column prop="brxm" label="姓名" min-width="80"></el-table-column>
        <el-table-column prop="age" label="年龄" min-width="60">
          <template slot-scope="scope">
            <span>{{ scope.row.age || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="brxb" label="性别" min-width="60"></el-table-column>
        <el-table-column prop="zkid" label="专科" min-width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.zkid || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="bqid" label="病区" min-width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.bqid || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="cwh" label="床位" min-width="60"></el-table-column>
        <el-table-column prop="niShiSSMC" label="拟施手术" min-width="150"></el-table-column>
        <el-table-column prop="zdysxm" label="主刀" min-width="80"></el-table-column>
        <el-table-column prop="tszdxm" label="台上指导" min-width="80"></el-table-column>
        <el-table-column prop="d1zsxm" label="一助" min-width="80"></el-table-column>
        <el-table-column prop="d2zsxm" label="二助" min-width="80"></el-table-column>
        <el-table-column prop="d3zsxm" label="三助" min-width="80"></el-table-column>
        <el-table-column prop="d4zsxm" label="四助" min-width="80"></el-table-column>
        <el-table-column prop="surgicalConsultation" label="术中会诊" min-width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.surgicalConsultation || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="mzys1xm" label="主麻" min-width="80"></el-table-column>
        <el-table-column prop="mzys2xm" label="二麻" min-width="80"></el-table-column>
        <el-table-column prop="mzys3xm" label="三麻" min-width="80"></el-table-column>
        <el-table-column prop="xhhs1xm" label="巡回" min-width="80"></el-table-column>
        <el-table-column prop="preOpWaitTime" label="术前等待时长" min-width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.preOpWaitTime || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="yjsssc" label="预计时长(分钟)" min-width="100"></el-table-column>
        <el-table-column prop="zhuangTaiMC" label="状态" min-width="80"></el-table-column>
      </el-table>
    </div>
  </div>
</template>
