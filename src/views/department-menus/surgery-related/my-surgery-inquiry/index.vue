<template>
  <!-- 我的相关手术查询 -->
  <div class="container">
    <div class="filter-container">
      <div class="filter-col">
        <!-- 日期范围选择 -->
        <div class="filter-item">
          <span class="filter-label">手术日期：</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            size="mini"
            style="width: 240px"
          ></el-date-picker>
        </div>

        <!-- 病人姓名 -->
        <div class="filter-item">
          <span class="filter-label">病人姓名：</span>
          <el-input
            v-model="patientName"
            placeholder="请输入病人姓名"
            size="mini"
            style="width: 150px"
            clearable
          ></el-input>
        </div>
      </div>

      <div class="filter-col">
        <!-- 住院号 -->
        <div class="filter-item">
          <span class="filter-label">住院号：</span>
          <el-input
            v-model="admissionNumber"
            placeholder="请输入住院号"
            size="mini"
            style="width: 150px"
            clearable
          ></el-input>
        </div>

        <!-- 病人专科 -->
        <div class="filter-item">
          <span class="filter-label">病人专科：</span>
          <el-select
            v-model="selectedDepartment"
            placeholder="请选择专科"
            size="mini"
            style="width: 150px"
            clearable
          >
            <el-option
              v-for="item in [{ buMenID: '', buMenMC: '所有专科' }, ...departmentList]"
              :key="item.buMenID"
              :label="item.buMenMC"
              :value="item.buMenID"
            ></el-option>
          </el-select>
        </div>
      </div>

      <div class="filter-col">
        <div class="filter-item">
          <el-button type="primary" size="mini" class="purple-button" @click="handleQuery">
            查询
          </el-button>
          <el-button type="success" size="mini" @click="exportToExcel">导出Excel</el-button>
        </div>
      </div>
    </div>

    <!-- 数据展示区域 -->
    <div class="table-container">
      <div class="title">
        <span>我的相关手术查询</span>
        <span class="total-count">共{{ tableData.length }}条数据</span>
      </div>
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 30px)"
        size="mini"
        v-loading="loading"
      >
        <el-table-column prop="shouShuJianMC" label="手术间" min-width="80"></el-table-column>
        <el-table-column prop="zyh" label="住院号" min-width="100"></el-table-column>
        <el-table-column prop="brxm" label="姓名" min-width="80"></el-table-column>
        <el-table-column prop="age" label="年龄" min-width="60">
          <template #default="{ row }">
            <span>{{ row.age || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="brxb" label="性别" min-width="60"></el-table-column>
        <el-table-column prop="zkid" label="专科" min-width="80">
          <template #default="{ row }">
            <span>{{ row.zkid || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="bqid" label="病区" min-width="80">
          <template #default="{ row }">
            <span>{{ row.bqid || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="cwh" label="床位" min-width="60"></el-table-column>
        <el-table-column prop="niShiSSMC" label="拟施手术" min-width="150"></el-table-column>
        <el-table-column prop="zdysxm" label="主刀" min-width="80"></el-table-column>
        <el-table-column prop="tszdxm" label="台上指导" min-width="80"></el-table-column>
        <el-table-column prop="d1zsxm" label="一助" min-width="80"></el-table-column>
        <el-table-column prop="d2zsxm" label="二助" min-width="80"></el-table-column>
        <el-table-column prop="d3zsxm" label="三助" min-width="80"></el-table-column>
        <el-table-column prop="d4zsxm" label="四助" min-width="80"></el-table-column>
        <el-table-column prop="surgicalConsultation" label="术中会诊" min-width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.surgicalConsultation || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="mzys1xm" label="主麻" min-width="80"></el-table-column>
        <el-table-column prop="mzys2xm" label="二麻" min-width="80"></el-table-column>
        <el-table-column prop="mzys3xm" label="三麻" min-width="80"></el-table-column>
        <el-table-column prop="xhhs1xm" label="巡回" min-width="80"></el-table-column>
        <el-table-column prop="preOpWaitTime" label="术前等待时长" min-width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.preOpWaitTime || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="yjsssc" label="预计时长(分钟)" min-width="100"></el-table-column>
        <el-table-column prop="zhuangTaiMC" label="状态" min-width="80"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getMySurgeryList } from '@/api/sugery-related'
import { getZhuanKeList } from '@/api/medical-quality'
import { mapState } from 'vuex'
import { format } from 'date-fns'
import * as XLSX from 'xlsx'

export default {
  name: 'MySurgeryInquiry',
  data() {
    return {
      // 查询条件
      dateRange: [],
      patientName: '',
      admissionNumber: '',
      selectedDepartment: '',
      // 专科列表
      departmentList: [],
      // 表格数据
      tableData: [],
      // 加载状态
      loading: false
    }
  },
  computed: {
    ...mapState({
      initInfo: ({ patient }) => patient.initInfo,
      zhuanKeList: ({ patient }) => patient.zhuanKeList
    })
  },
  created() {
    this.init()
  },
  methods: {
    // 初始化
    async init() {
      // 设置默认日期为当天
      const today = format(new Date(), 'yyyy-MM-dd')
      this.dateRange = [today, today]

      // 获取专科列表
      await this.getDepartmentList()

      // 初始查询
      await this.handleQuery()
    },

    // 获取专科列表
    async getDepartmentList() {
      try {
        // 先尝试从store获取
        if (this.zhuanKeList && this.zhuanKeList.length > 0) {
          this.departmentList = this.zhuanKeList
        } else {
          // 如果store中没有，则调用API获取
          const res = await getZhuanKeList()
          if (res && res.hasError === 0 && res.data) {
            this.departmentList = res.data
            // 同时更新store
            this.$store.commit('patient/GET_ZHUAN_KE_LIST', res.data)
          }
        }
      } catch (error) {
        console.error('获取专科列表失败:', error)
        this.departmentList = []
      }
    },

    // 查询数据
    async handleQuery() {
      try {
        this.loading = true

        const params = {}

        // 日期范围
        if (this.dateRange && this.dateRange.length === 2) {
          params.startDate = this.dateRange[0]
          params.endDate = this.dateRange[1]
        }

        // 病人姓名
        if (this.patientName.trim()) {
          params.patientName = this.patientName.trim()
        }

        // 住院号
        if (this.admissionNumber.trim()) {
          params.zhuYuanHao = this.admissionNumber.trim()
        }

        // 专科
        if (this.selectedDepartment) {
          params.departmentId = this.selectedDepartment
        }

        const res = await getMySurgeryList(params)

        this.loading = false

        if (res && res.hasError === 0 && res.data) {
          this.tableData = res.data || []
        } else {
          this.tableData = []
          if (res?.errorMessage) {
            this.$message.warning(res.errorMessage)
          }
        }
      } catch (error) {
        this.loading = false
        this.$message.error(error.errorMessage || '查询数据失败')
        this.tableData = []
      }
    },

    // 导出Excel
    exportToExcel() {
      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      try {
        // 准备导出数据
        const exportData = this.tableData.map((item) => {
          return {
            手术间: item.shouShuJianMC || '',
            住院号: item.zyh || '',
            姓名: item.brxm || '',
            年龄: item.age || '',
            性别: item.brxb || '',
            专科: item.zkid || '',
            病区: item.bqid || '',
            床位: item.cwh || '',
            拟施手术: item.niShiSSMC || '',
            主刀: item.zdysxm || '',
            台上指导: item.tszdxm || '',
            一助: item.d1zsxm || '',
            二助: item.d2zsxm || '',
            三助: item.d3zsxm || '',
            四助: item.d4zsxm || '',
            术中会诊: item.surgicalConsultation || '',
            主麻: item.mzys1xm || '',
            二麻: item.mzys2xm || '',
            三麻: item.mzys3xm || '',
            巡回: item.xhhs1xm || '',
            术前等待时长: item.preOpWaitTime || '',
            预计时长: item.yjsssc ? `${item.yjsssc}分钟` : '',
            状态: item.zhuangTaiMC || ''
          }
        })

        // 创建工作表
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

        // 生成文件名
        const fileName = `我的相关手术查询_${format(new Date(), 'yyyy-MM-dd')}.xlsx`

        // 下载文件
        XLSX.writeFile(workbook, fileName)
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  padding: 10px;
  background: #fff;
}

.filter-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  display: flex;
  flex-wrap: wrap;
}

.filter-col {
  display: flex;
  padding: 0 8px;
  margin-bottom: 8px;

  .filter-item {
    display: flex;
    align-items: center;
    margin-right: 15px;
  }
}

.filter-label {
  font-weight: bold;
  min-width: 80px;
  white-space: nowrap;
}

.purple-button {
  background: #a66dd4;
  border: 1px solid #a66dd4;
  &:hover,
  &:focus {
    background: #ce8be0;
    border-color: #ce8be0;
  }
}

.table-container {
  height: calc(100% - 82px);
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .total-count {
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
