# 我的相关手术查询

## 功能说明

本页面用于查询和展示与当前用户相关的手术信息，提供多种查询条件和数据导出功能。

## 查询条件

### 1. 日期范围选择
- **手术日期**：支持选择开始日期和结束日期
- **默认值**：当天日期
- **组件类型**：日期范围选择器

### 2. 病人信息查询
- **病人姓名**：支持模糊查询
- **病案号**：精确查询
- **病人专科**：下拉选择，支持选择所有专科

## 数据展示字段

### 基本信息
1. **手术间**（`shouShuJian`）：手术室名称
2. **住院号**（`bingAnHao`）：患者病案号
3. **姓名**（`bingRenXM`）：患者姓名
4. **年龄**：通过`chuShengRQ`（出生日期）计算获得
5. **性别**（`bingRenXBMC`）：患者性别
6. **专科**（`zhuanKeMC`）：患者所属专科名称
7. **病区**（`bingQuMC`）：患者所在病区名称
8. **床位**（`chuangWeiHao`）：患者床位号

### 手术信息
9. **拟施手术**（`niShiSS[0].shouShuMC`）：计划进行的手术名称（取数组第一个元素）
10. **预计时长**（`yuJiSSSC`）：预计手术时长（分钟）
11. **状态**（`zhuangTaiBZ`）：手术当前状态

### 医护人员
12. **主刀**（`zhuDaoYSXM`）：主刀医生姓名
13. **台上指导**（`taiShangZDXM`）：台上指导医生姓名
14. **一助**（`diYiZSXM`）：第一助手姓名
15. **二助**（`diErZSXM`）：第二助手姓名
16. **三助**（`diSanZSXM`）：第三助手姓名
17. **四助**（`diSiZSXM`）：第四助手姓名
18. **主麻**（`maZuiYS1XM`）：主麻醉师姓名
19. **二麻**（`maZuiYS2XM`）：第二麻醉师姓名
20. **三麻**（`maZuiYS3XM`）：第三麻醉师姓名
21. **巡回**（`xunHuiHS1XM`）：巡回护士姓名

### 其他字段
22. **术中会诊**（`shuZhongHZ`）：术中会诊信息
23. **术前等待时长**（`shuQianDDSC`）：术前等待时间

## 技术实现

### API接口
- **接口方法**：`getBenRenXGSS`
- **接口文件**：`@/api/surgery-related.js`
- **请求参数**：
  - `kaiShiSJ`：开始时间
  - `jieShuSJ`：结束时间
  - `bingRenXM`：病人姓名
  - `bingAnHao`：病案号
  - `zhuanKeID`：专科ID
  - `yiShengRykID`：医生人员库ID（从doctorInfo.renYuanKID获取）

### 数据处理
- 自动设置默认查询日期为当天
- 支持清空查询条件
- 专科列表直接从Vuex store获取
- 使用现代Vue插槽语法（`#default="{ row }"`）
- 简化的接口响应处理逻辑
- 年龄通过出生日期实时计算

### 特殊字段处理
- **年龄计算**：通过`calculateAge()`方法计算当前日期与出生日期的差值
- **拟施手术**：从`niShiSS`数组中取第一个元素的`shouShuMC`字段
- **空值处理**：所有空值字段显示为"-"

## 主要功能

### 1. 查询功能
- 支持多条件组合查询
- 实时数据获取
- 查询结果统计显示

### 2. 导出功能
- **Excel导出**：将查询结果导出为Excel文件
- **文件命名**：自动生成带日期的文件名
- **数据格式化**：预计时长添加"分钟"单位，年龄实时计算

### 3. 界面特性
- 响应式表格布局
- 错误提示处理
- 清空输入功能
- 直接使用store数据，无需额外API调用

## 字段映射更新

### 请求参数映射
- `startDate` → `kaiShiSJ`（开始时间）
- `endDate` → `jieShuSJ`（结束时间）
- `patientName` → `bingRenXM`（病人姓名）
- `zhuYuanHao` → `bingAnHao`（病案号）
- `departmentId` → `zhuanKeID`（专科ID）
- `rykid` → `yiShengRykID`（医生人员库ID）

### 表格字段映射
- `shouShuJianMC` → `shouShuJian`（手术间）
- `zyh` → `bingAnHao`（住院号/病案号）
- `brxm` → `bingRenXM`（姓名）
- `brxb` → `bingRenXBMC`（性别）
- `zkid` → `zhuanKeMC`（专科）
- `bqid` → `bingQuMC`（病区）
- `cwh` → `chuangWeiHao`（床位）
- `niShiSSMC` → `niShiSS[0].shouShuMC`（拟施手术）
- `zdysxm` → `zhuDaoYSXM`（主刀）
- `tszdxm` → `taiShangZDXM`（台上指导）
- `d1zsxm` → `diYiZSXM`（一助）
- `d2zsxm` → `diErZSXM`（二助）
- `d3zsxm` → `diSanZSXM`（三助）
- `d4zsxm` → `diSiZSXM`（四助）
- `mzys1xm` → `maZuiYS1XM`（主麻）
- `mzys2xm` → `maZuiYS2XM`（二麻）
- `mzys3xm` → `maZuiYS3XM`（三麻）
- `xhhs1xm` → `xunHuiHS1XM`（巡回）
- `yjsssc` → `yuJiSSSC`（预计时长）
- `zhuangTaiMC` → `zhuangTaiBZ`（状态）

## 使用说明

1. **初始化**：页面加载时自动设置当天日期并执行查询
2. **条件查询**：设置查询条件后点击"查询"按钮
3. **数据导出**：点击"导出Excel"按钮下载数据
4. **清空条件**：使用输入框的清空功能重置条件

## 注意事项

- 确保用户已登录并有相应权限
- 日期范围不能为空
- 导出功能需要浏览器支持文件下载
- 年龄通过出生日期实时计算，无出生日期时显示"-"
- 拟施手术字段为数组格式，取第一个元素显示
- 异常情况会显示相应的错误提示
