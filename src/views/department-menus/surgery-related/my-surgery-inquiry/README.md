# 我的相关手术查询

## 功能说明

本页面用于查询和展示与当前用户相关的手术信息，提供多种查询条件和数据导出功能。

## 查询条件

### 1. 日期范围选择
- **手术日期**：支持选择开始日期和结束日期
- **默认值**：当天日期
- **组件类型**：日期范围选择器

### 2. 病人信息查询
- **病人姓名**：支持模糊查询
- **住院号**：精确查询
- **病人专科**：下拉选择，支持选择所有专科

## 数据展示字段

### 基本信息
1. **手术间**（`shouShuJianMC`）：手术室名称
2. **住院号**（`zyh`）：患者住院编号
3. **姓名**（`brxm`）：患者姓名
4. **年龄**：患者年龄（暂时为空）
5. **性别**（`brxb`）：患者性别
6. **专科**（`zkid`）：患者所属专科（暂时显示ID）
7. **病区**（`bqid`）：患者所在病区（暂时显示ID）
8. **床位**（`cwh`）：患者床位号

### 手术信息
9. **拟施手术**（`niShiSSMC`）：计划进行的手术名称
10. **预计时长**（`yjsssc`）：预计手术时长（分钟）
11. **状态**（`zhuangTaiMC`）：手术当前状态

### 医护人员
12. **主刀**（`zdysxm`）：主刀医生姓名
13. **台上指导**（`tszdxm`）：台上指导医生姓名
14. **一助**（`d1zsxm`）：第一助手姓名
15. **二助**（`d2zsxm`）：第二助手姓名
16. **三助**（`d3zsxm`）：第三助手姓名
17. **四助**（`d4zsxm`）：第四助手姓名
18. **主麻**（`mzys1xm`）：主麻醉师姓名
19. **二麻**（`mzys2xm`）：第二麻醉师姓名
20. **三麻**（`mzys3xm`）：第三麻醉师姓名
21. **巡回**（`xhhs1xm`）：巡回护士姓名

### 待补充字段
22. **术中会诊**：术中会诊信息（暂时为空）
23. **术前等待时长**：术前等待时间（暂时为空）

## 技术实现

### API接口
- **接口方法**：`getMySurgeryList`
- **接口文件**：`@/api/sugery-related.js`
- **请求参数**：
  - `startDate`：开始日期
  - `endDate`：结束日期
  - `patientName`：病人姓名
  - `zhuYuanHao`：住院号
  - `departmentId`：专科ID

### 数据处理
- 自动设置默认查询日期为当天
- 支持清空查询条件
- 空值字段显示为"-"
- 加载状态显示
- 使用现代Vue插槽语法（`#default="{ row }"`）

## 主要功能

### 1. 查询功能
- 支持多条件组合查询
- 实时数据获取
- 查询结果统计显示

### 2. 导出功能
- **Excel导出**：将查询结果导出为Excel文件
- **文件命名**：自动生成带日期的文件名
- **数据格式化**：预计时长添加"分钟"单位

### 3. 界面特性
- 响应式表格布局
- 加载状态指示
- 错误提示处理
- 清空输入功能

## 样式设计

参考了 `admission-24h-screening-query` 页面的设计风格：
- 筛选区域使用浅蓝色背景
- 表格区域带有阴影效果
- 紫色主题查询按钮
- 绿色导出按钮
- 响应式布局设计

## 使用说明

1. **初始化**：页面加载时自动设置当天日期并执行查询
2. **条件查询**：设置查询条件后点击"查询"按钮
3. **数据导出**：点击"导出Excel"按钮下载数据
4. **清空条件**：使用输入框的清空功能重置条件

## 注意事项

- 确保用户已登录并有相应权限
- 日期范围不能为空
- 导出功能需要浏览器支持文件下载
- 部分字段（年龄、专科名称、病区名称、术中会诊、术前等待时长）暂时为空，等待后端补齐
- 异常情况会显示相应的错误提示
