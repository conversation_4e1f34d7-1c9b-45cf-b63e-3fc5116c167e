<template>
  <div class="container">
    <div class="header">
      <el-radio v-model="inType" label="2" @change="inTypeSelect">按病人姓名</el-radio>
      <el-radio v-model="inType" label="1" @change="inTypeSelect">按床位号</el-radio>
      <!-- <div class="button">
        <el-button type="primary" @click="searchFun">查询</el-button>
      </div> -->
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">在院病人一览表</div>
      </div>
      <div class="table">
        <el-table max-height="688" border stripe :data="patientData" style="width: 1476px">
          <el-table-column prop="bingQuMC" width="130" label="病区"></el-table-column>
          <el-table-column prop="chuangWeiHao" width="120" label="床号"></el-table-column>
          <el-table-column prop="bingRenXM" width="120" label="姓名"></el-table-column>
          <el-table-column prop="huLiJB" width="130" label="护理级别">
            <template #default="{ row }">
              <el-tag v-if="row.huLiJB == '1'" type="danger">特级护理</el-tag>
              <el-tag v-if="row.huLiJB == '2'" type="success">一级护理</el-tag>
              <el-tag v-if="row.huLiJB == '3'" type="warning">二级护理</el-tag>
              <el-tag v-if="row.huLiJB == '4'">三级护理</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="jieSuanLX" width="130" label="结算类型"></el-table-column>
          <el-table-column prop="shouShu3Day" width="200" label="近3天是否有手术通知单">
            <template #default="{ row }">
              <span>{{ row.shouShu3Day == '1' ? '有' : '无' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="zaiYuanTS" width="120" label="在院天数">
            <template #default="{ row }">{{ row.zaiYuanTS }} 天</template>
          </el-table-column>
          <el-table-column prop="zongZhuYuanTS" width="120" label="总住院天数">
            <template #default="{ row }">{{ row.zongZhuYuanTS }} 天</template>
          </el-table-column>
          <el-table-column
            prop="yiFengCun"
            width="170"
            label="入院记录是否已书写"
          ></el-table-column>
          <el-table-column prop="ruYuanJLUnfinished" width="120" label="封存状态"></el-table-column>
          <el-table-column prop="caoZuo" width="114" align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { admission } from '@/api/specialized-patients'
import { format, subDays } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      inType: '1',
      patientData: []
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit
    })
  },
  async mounted() {
    await this.admission()
  },
  methods: {
    // 获取列表初始化接口
    async admission() {
      try {
        const res = await admission({
          paiXuFS: this.inType
        })
        if (res.hasError === 0) {
          this.patientData = res.data
          this.delArr()
        }
      } catch (error) {
        console.log(error)
      }
    },

    delArr() {
      this.patientData = this.patientData.map((item) => {
        return {
          ...item,
          zaiYuanTS: Math.ceil(item.zaiYuanTS),
          zongZhuYuanTS: Math.ceil(item.zongZhuYuanTS)
        }
      })
    },

    inTypeSelect(value) {
      this.inType = value
      this.admission()
    },

    searchFun() {},

    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 740px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}

::v-deep .el-table__cell {
  padding-left: 8px;
  padding-top: 4px;
  padding-bottom: 4px;
}

::v-deep .el-table__cell:last-child {
  padding-left: 0px;
}

::v-deep .el-table th.el-table__cell,
.el-table td.el-table__cell {
  padding-top: 9px;
  padding-bottom: 9px;
}

::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
  padding-top: 4px;
  padding-bottom: 4px;
}

::v-deep .el-radio__original {
  display: none !important;
}
</style>
