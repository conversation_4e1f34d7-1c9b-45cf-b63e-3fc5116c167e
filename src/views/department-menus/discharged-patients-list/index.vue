<template>
  <div class="container">
    <div class="header">
      <el-select v-model="ZhiLiaoZu">
        <el-option
          v-for="item in ZhiLiaoZuList"
          :key="item.zhiLiaoZuID"
          :label="item.zhiLiaoZuMC"
          :value="item.zhiLiaoZuID"
        ></el-option>
      </el-select>
      <div class="query-word">时间范围：</div>
      <el-date-picker
        v-model="zhuanKeSJ"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="changeDay"
      ></el-date-picker>
      <div class="query-word">输入姓名:</div>
      <div>
        <el-input v-model="xingMing"></el-input>
      </div>
      <div class="query-word">输入病案号：</div>
      <div>
        <el-input v-model="bingAnHao"></el-input>
      </div>
      <!-- <div class="query-word">输入住院号：</div>
      <div>
        <el-input v-model="zhuYuanHao"></el-input>
      </div> -->
      <div class="button">
        <el-button type="primary" @click="searchFun">查询</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">15天内出院及15天外为归档病人一览表</div>
      </div>
      <div class="table">
        <el-table max-height="688" border stripe :data="patientData" style="width: 100%">
          <el-table-column prop="empi" width="120" label="病案号"></el-table-column>
          <el-table-column prop="bingQuMC" width="130" label="病区"></el-table-column>
          <el-table-column prop="zhiLiaoZuMC" width="160" label="治疗组"></el-table-column>
          <el-table-column prop="chuangWeiHao" width="120" label="床号"></el-table-column>
          <el-table-column prop="xingMing" width="120" label="姓名"></el-table-column>
          <el-table-column prop="xingBie" width="120" label="性别">
            <template #default="{ row }">
              <span>{{ row.xingBie == 1 ? '男' : '女' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="chuShengRQ" width="120" label="出生日期"></el-table-column>
          <el-table-column prop="ruYuanZD" width="270" label="入院诊断"></el-table-column>
          <el-table-column prop="bingQuRYRQ" width="120" label="入院日期"></el-table-column>
          <el-table-column prop="bingQuCYRQ" width="120" label="出院日期"></el-table-column>
          <el-table-column prop="hospitalDays" width="120" label="住院天数"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" width="120" label="病历状态">
            <template #default="{ row }">
              <el-tag v-if="row.zhuangTaiBZ == 1">已封存</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="guiDangBZ" width="120" label="医生归档">
            <template #default="{ row }">
              <span v-if="row.guiDangBZ == 0 || row.guiDangBZ == 1">已提交</span>
            </template>
          </el-table-column>
          <el-table-column prop="caoZuo" width="106" align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import {
  get15TNCYBRByTime,
  get15TNCYBRByZLZByTime,
  getZhiLiaoZuListByZhuanKeID
} from '@/api/specialized-patients'
import { format, subDays, differenceInDays, parseISO } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      ZhiLiaoZu: '',
      ZhiLiaoZuList: [],
      zhuanKeSJ: '',
      kaiShiSJ: format(subDays(new Date(), 15), 'yyyy-MM-dd'), //开始时间
      jieShuSJ: format(new Date(), 'yyyy-MM-dd'), //结束时间
      // kaiShiSJ: '2023-07-09', //开始时间
      // jieShuSJ: '2023-07-15', //结束时间
      xingMing: '',
      bingAnHao: '',
      zhuYuanHao: '',
      patientData: []
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit
    })
  },
  async mounted() {
    await this.get15TNCYBRByTime()
    await this.getZhiLiaoZuListByZhuanKeID()
  },
  methods: {
    // 获取列表初始化接口
    async get15TNCYBRByTime() {
      try {
        const res = await get15TNCYBRByTime({
          chuYuanKSSJ: this.kaiShiSJ + ' 00:00:00',
          chuYuanJSSJ: this.jieShuSJ + ' 23:59:59',
          zhuankeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.patientData = res.data
          this.patientData1 = res.data
          this.delArr()
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 查询列表接口
    async get15TNCYBRByZLZByTime() {
      try {
        const res = await get15TNCYBRByZLZByTime({
          chuYuanKSSJ: this.kaiShiSJ + ' 00:00:00',
          chuYuanJSSJ: this.jieShuSJ + ' 23:59:59',
          zhuanKeID: this.zhuanKeID,
          zhiLiaoZuID: this.ZhiLiaoZu
        })
        if (res.hasError === 0) {
          this.patientData1 = res.data
          this.patientData = this.patientData1.filter((item) => {
            // 1. 姓名匹配（模糊）
            let isXingMingMatch = true
            if (this.xingMing != '') {
              isXingMingMatch = item.xingMing.toLowerCase().includes(this.xingMing.toLowerCase())
            }

            // 2. 病案号匹配（模糊）
            let isBingAnHaoMatch = true
            if (this.bingAnHao != '') {
              isBingAnHaoMatch =
                item.empi != null && String(item.empi).includes(String(this.bingAnHao))
            }

            // 组合条件：任一条件满足即可（逻辑或）
            return isXingMingMatch && isBingAnHaoMatch
          })
          this.delArr()
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取专科治疗组列表
    async getZhiLiaoZuListByZhuanKeID() {
      try {
        const res = await getZhiLiaoZuListByZhuanKeID({
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.ZhiLiaoZuList = res.data.concat(
            {
              zhiLiaoZuID: 0,
              zhiLiaoZuMC: '未归属治疗组'
            },
            {
              zhiLiaoZuID: '-1',
              zhiLiaoZuMC: '转专科未处理治疗组'
            }
          )
        }
      } catch (error) {
        console.log(error)
      }
    },

    delArr() {
      this.patientData = this.patientData.map((item) => {
        const admissionDate = parseISO(item.bingQuRYRQ)
        const dischargeDate = parseISO(item.bingQuCYRQ)
        const hospitalDays = differenceInDays(dischargeDate, admissionDate) + 1
        return {
          ...item,
          chuShengRQ: format(new Date(item.chuShengRQ), 'yyyy-MM-dd'),
          bingQuRYRQ: format(new Date(item.bingQuRYRQ), 'yyyy-MM-dd'),
          bingQuCYRQ: format(new Date(item.bingQuCYRQ), 'yyyy-MM-dd'),
          hospitalDays
        }
      })
    },

    changeDay(e) {
      this.kaiShiSJ = format(new Date(e[0]), 'yyyy-MM-dd')
      this.jieShuSJ = format(new Date(e[1]), 'yyyy-MM-dd')
    },

    searchFun() {
      this.get15TNCYBRByZLZByTime()
    },

    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 740px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}

::v-deep .el-table__cell {
  padding-left: 8px;
}

::v-deep .el-table__cell:last-child {
  padding-left: 0px;
}

::v-deep .el-table th.el-table__cell,
.el-table td.el-table__cell {
  padding-top: 9px;
  padding-bottom: 9px;
}

::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
  padding-top: 4px;
  padding-bottom: 4px;
}
</style>
