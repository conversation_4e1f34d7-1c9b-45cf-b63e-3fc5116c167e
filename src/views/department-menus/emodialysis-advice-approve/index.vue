<template>
  <div class="container">
    <div class="header">
      <el-date-picker
        v-model="zhuanKeSJ"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      ></el-date-picker>
      <div class="query-word">姓名:</div>
      <div>
        <el-input v-model="xingMing"></el-input>
      </div>
      <div style="margin: 0 10px">
        <el-radio v-model="radio" label="1">未审批</el-radio>
        <el-radio v-model="radio" label="2">已审批</el-radio>
      </div>
      <div class="query-word">病人科室：</div>
      <div>
        <el-input v-model="bingAnHao"></el-input>
      </div>
      <div class="button">
        <el-button type="primary">查询</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">血透医嘱审批维护</div>
        <!-- <div class="button"><el-button type="primary">新增</el-button></div> -->
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData" style="width: 1706px">
          <el-table-column prop="bingRenXingMing" width="110" label="病人姓名"></el-table-column>
          <el-table-column prop="zhuanKe" width="120" label="专科"></el-table-column>
          <el-table-column prop="bingQu" width="120" label="病区"></el-table-column>
          <el-table-column prop="zaiYuanZhuangTai" width="110" label="在院状态"></el-table-column>
          <el-table-column prop="yiZhuSJ" width="160" label="医嘱时间"></el-table-column>
          <el-table-column prop="shenPiSJ" width="160" label="审批时间"></el-table-column>
          <el-table-column prop="zhiXingPingLv" width="160" label="执行频率"></el-table-column>
          <el-table-column prop="shenPiJieGuo" width="160" label="审批结果"></el-table-column>
          <el-table-column prop="shenPiBeiZhu" width="250" label="审批备注"></el-table-column>
          <el-table-column prop="jiXuTongGuo" width="250" label="继续通过"></el-table-column>
          <el-table-column prop="caoZuo" width="104" label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      query: '',
      zhuanKeSJ: '',
      xingMing: '',
      bingAnHao: '',
      radio: '1',
      tableData: [
        {
          bingRenXingMing: '吴德兴',
          zhuanKe: '内科',
          bingQu: 'A262病区',
          zaiYuanZhuangTai: '在院',
          yiZhuSJ: '2022-03-15 08:14:57',
          shenPiSJ: '2022-03-17 15:26:19',
          zhiXingPingLv: '每周3次',
          shenPiJieGuo: '通过',
          shenPiBeiZhu: '测试',
          jiXuTongGuo: '好的很好'
        }
      ]
    }
  },
  methods: {
    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 700px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 1476px;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}
</style>
