<template>
  <div class="container">
    <div class="header">
      <div class="query-word">治疗组：</div>
      <el-select v-model="query">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <div class="query-word">转科时间：</div>
      <el-date-picker
        v-model="zhuanKeSJ"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      ></el-date-picker>
      <div class="button">
        <el-button type="primary">查询</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">专科本治疗组特检报告查询维护</div>
        <!-- <div class="button"><el-button type="primary">新增</el-button></div> -->
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData" style="width: 836px">
          <el-table-column prop="bingRenXingMing" width="130" label="病人姓名"></el-table-column>
          <el-table-column prop="bingAnHao" width="130" label="病案号"></el-table-column>
          <el-table-column prop="bingQuChuangWei" width="150" label="病区-床位号"></el-table-column>
          <el-table-column prop="jianChaLeiXing" width="150" label="检查类型"></el-table-column>
          <el-table-column prop="baoGaoSJ" width="170" label="报告时间"></el-table-column>
          <el-table-column prop="caoZuo" width="104" label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: '选项1',
          label: 'ICU徐红蕾组'
        },
        {
          value: '选项2',
          label: 'ICU张三组'
        }
      ],
      zhuanKeSJ: '',
      tableData: [
        {
          bingRenXingMing: '潘时章',
          bingAnHao: '0015314056',
          bingQuChuanWei: 'A232病区-001',
          jianChaLeiXing: '超声介入',
          baoGaoSJ: '2023-07-13 14:22:30'
        }
      ]
    }
  },
  methods: {
    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 700px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 1476px;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}
</style>
