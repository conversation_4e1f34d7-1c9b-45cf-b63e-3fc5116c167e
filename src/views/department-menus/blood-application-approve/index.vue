<template>
  <div class="container">
    <el-tabs v-model="activeName" type="card" @tab-click="changeTabClick">
      <el-tab-pane label="当日800ml以内" name="item1"></el-tab-pane>
      <el-tab-pane label="当日800ml-1600ml" name="item2"></el-tab-pane>
      <el-tab-pane label="当日1600ml及以上" name="item3"></el-tab-pane>
      <el-tab-pane label="无需审批" name="item4"></el-tab-pane>
      <el-tab-pane label="全部申请量" name="item5"></el-tab-pane>
    </el-tabs>
    <div class="header">
      <!-- 审批状态 -->
      <div v-if="activeName != 'item4'" class="query-word">审批状态：</div>
      <div v-if="activeName != 'item4'">
        <el-radio v-model="tabStates[activeName].radio" label="0">全部</el-radio>
        <el-radio
          v-if="activeName == 'item1' || activeName == 'item2' || activeName == 'item5'"
          v-model="tabStates[activeName].radio"
          label="1"
        >
          上级医师审批
        </el-radio>
        <el-radio
          v-if="activeName == 'item2' || activeName == 'item3' || activeName == 'item5'"
          v-model="tabStates[activeName].radio"
          label="2"
        >
          科室审批
        </el-radio>
        <el-radio
          v-if="activeName == 'item3' || activeName == 'item5'"
          v-model="tabStates[activeName].radio"
          label="3"
        >
          医务科/输血科审批
        </el-radio>
        <el-radio v-if="activeName == 'item5'" v-model="tabStates[activeName].radio" label="4">
          无需审批
        </el-radio>
        <el-radio v-if="activeName != 'item4'" v-model="tabStates[activeName].radio" label="5">
          已审批
        </el-radio>
      </div>
      <!-- 选择日期 -->
      <div class="query-word">选择日期：</div>
      <el-date-picker
        v-model="tabStates[activeName].dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd HH:mm:ss"
        @change="onChangeDate"
      ></el-date-picker>
      <!-- 选择专科 -->
      <div class="query-word">选择专科：</div>
      <el-select v-model="tabStates[activeName].query">
        <el-option
          v-for="item in options"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        ></el-option>
      </el-select>
      <!-- 与本人相关 -->
      <div style="margin: 0 10px">
        <el-checkbox v-model="tabStates[activeName].checked">与本人相关</el-checkbox>
      </div>
      <div class="button">
        <el-button type="primary" @click="searchFun">查询</el-button>
      </div>
      <div class="button">
        <el-button type="primary" style="background: #356ac5">导出</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">全部申请量</div>
      </div>
      <div class="table">
        <el-table
          max-height="648"
          border
          :data="tabStates[activeName].tableData"
          style="width: 1476px"
        >
          <el-table-column prop="leiBie" width="110" label="类型">
            <template #default="{ row }">
              <el-tag :type="row.leiBie === '门诊' ? '' : 'success'" effect="dark">
                {{ row.leiBie }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="bingRenXM" width="110" label="病人姓名"></el-table-column>
          <el-table-column prop="bingRenXM" width="110" label="性别"></el-table-column>
          <el-table-column prop="shenQingSJ" width="160" label="开单时间"></el-table-column>
          <el-table-column prop="shenQingYSXM" width="110" label="开单医师"></el-table-column>
          <el-table-column prop="kdzk" width="110" label="开单专科"></el-table-column>
          <el-table-column prop="shenQingSL" width="240" label="申请类型数量"></el-table-column>
          <el-table-column prop="sjfxl" width="110" label="实际发血量"></el-table-column>
          <el-table-column prop="sxleiBie" width="160" label="输血类型"></el-table-column>

          <el-table-column prop="spzt" width="160" label="审批状态">
            <template #default="{ row }">
              <el-tag v-if="row.spzt === '待科室审批'" type="warning" effect="light">
                {{ row.spzt }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="sjysspzt" width="200" label="上级医师审批状态">
            <template #default="{ row }">
              <el-tag v-if="row.sjysspzt === '通过'" type="" effect="light">
                {{ row.sjysspzt }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column v-if="activeName == 'item1'" prop="spyj" width="200" label="审批意见">
            <template #default="{ row }">
              <el-tag v-if="row.spyj === '同意'" type="" effect="light">
                {{ row.spyj }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column v-else prop="sjysspyj" width="200" label="上级医师审批意见">
            <template #default="{ row }">
              <el-tag v-if="row.sjysspyj === '同意'" type="" effect="light">
                {{ row.sjysspyj }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="sjspys" width="200" label="上级审批医师"></el-table-column>
          <el-table-column
            v-if="activeName == 'item1'"
            prop="sjysspsj"
            width="200"
            label="审批时间"
          ></el-table-column>
          <el-table-column
            v-else
            prop="sjysspsj"
            width="200"
            label="上级医师审批时间"
          ></el-table-column>

          <div v-if="activeName == 'item2' || activeName == 'item3'">
            <el-table-column prop="ksspzt" width="200" label="科室审批状态"></el-table-column>
            <el-table-column prop="ksspyj" width="200" label="科室审批意见"></el-table-column>
            <el-table-column prop="ksspys" width="200" label="科室审批医师"></el-table-column>
            <el-table-column prop="ksspsj" width="200" label="科室审批时间"></el-table-column>
          </div>
          <div v-if="activeName == 'item3' || activeName == 'item5'">
            <el-table-column prop="sxkspzt" width="200" label="输血科审批状态"></el-table-column>
            <el-table-column prop="sxkspyj" width="200" label="输血科审批意见"></el-table-column>
            <el-table-column prop="sxkspry" width="200" label="输血科审批人员"></el-table-column>
            <el-table-column prop="sxkspsj" width="200" label="输血科审批时间"></el-table-column>
            <el-table-column prop="ywkspzt" width="200" label="医务科审批状态"></el-table-column>
            <el-table-column prop="ywkspyj" width="200" label="医务科审批意见"></el-table-column>
            <el-table-column prop="ywkspry" width="200" label="医务科审批人员"></el-table-column>
            <el-table-column prop="ywkspsj" width="200" label="医务科审批时间"></el-table-column>
          </div>
          <el-table-column v-if="activeName == 'item1'" prop="caoZuo" width="104" label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">审批</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="tabStates[activeName].currentPage"
          :page-sizes="[10, 20, 50]"
          :page-size="tabStates[activeName].pageSize"
          :total="tabStates[activeName].total"
          layout="total, sizes, prev, pager, next"
          @current-change="(val) => handlePageChange(val, 'currentPage')"
          @size-change="(val) => handlePageChange(val, 'pageSize')"
        ></el-pagination>
      </div>
    </div>
    <div class="bottom">
      <div style="margin-bottom: 5px">
        <i class="el-icon-warning" style="color: #356ac5"></i>
        <span class="title-tips">非紧急用血执行分级申请审批管理：</span>
      </div>
      <div class="tips">1.同一患者同一天申请量≤800ml，需上级医师核准签发；</div>
      <div class="tips">
        2.同一患者同一天申请量介入800-1600ml，需上级医师审核，并经科主任核准签发；
      </div>
      <div class="tips">
        3.同一患者同一天申请量≥1600ml，科主任核准签发后，经输血科审核确认，报医务处审批，方可备血。
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible" width="40%" :before-close="handleClose">
      <div slot="title" class="dialog-title">审核</div>
      <table>
        <tbody>
          <tr>
            <td class="info-label">名称:</td>
            <td class="info-value">
              <el-input v-model="mingCheng"></el-input>
            </td>
            <td class="info-label">类别:</td>
            <td class="info-value">
              <div class="flex">
                <el-radio v-model="leiBie" label="1">普通记录</el-radio>
                <el-radio v-model="leiBie" label="2">操作记录</el-radio>
              </div>
            </td>
          </tr>
          <tr>
            <td class="info-label">内容:</td>
            <td colspan="3" class="info-value">
              <el-input
                v-model="neiRong"
                type="textarea"
                :autosize="{ maxRows: 15 }"
                placeholder="请输入内容"
                maxlength="1000"
                show-word-limit
                :clearable="true"
              ></el-input>
            </td>
          </tr>
          <tr>
            <td class="info-label">状态:</td>
            <td class="info-value">
              <div class="flex">
                <el-radio v-model="zhuangTaiBZ" label="0">启用</el-radio>
                <el-radio v-model="zhuangTaiBZ" label="1">停用</el-radio>
              </div>
            </td>
            <td class="info-label">关联操作名称:</td>
            <td class="info-value no-padding">
              <div class="flex">
                <span class="no-padding-label">内镜下肠息肉切除术</span>
                <el-button type="primary">选择</el-button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <div class="bottom-btn-group">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary">关联专科</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  approval,
  getApprovalAll,
  getApprovalFromKeShi,
  getApprovalFromShangJiYiShi,
  getApprovalFromYiWuKe,
  getApprovalWithoutSP,
  getBaseInfo
} from '@/api/system-maintenance'
export default {
  data() {
    return {
      dialogVisible: false,
      activeName: 'item1',
      radio: '1',
      dateS: '2022-03-15 00:00:00',
      dateE: '2025-03-15 00:00:00',
      query: '选项1',
      checked: false,
      tableData: [],
      totalNum: 0,
      // 公用数据
      tabStates: {
        item1: {
          radio: '0',
          dateS: '2022-03-15 00:00:00',
          dateE: '2025-03-15 00:00:00',
          query: '选项1',
          checked: false,
          tableData: [],
          currentPage: 1,
          pageSize: 10,
          total: 0
        },
        item2: {
          radio: '0',
          dateS: '2022-03-15 00:00:00',
          dateE: '2025-03-15 00:00:00',
          query: '选项1',
          checked: false,
          tableData: [],
          currentPage: 1,
          pageSize: 10,
          total: 0
        },
        item3: {
          radio: '0',
          dateS: '2022-03-15 00:00:00',
          dateE: '2025-03-15 00:00:00',
          query: '选项1',
          checked: false,
          tableData: [],
          currentPage: 1,
          pageSize: 10,
          total: 0
        },
        item4: {
          radio: '0',
          dateS: '2022-03-15 00:00:00',
          dateE: '2025-03-15 00:00:00',
          query: '选项1',
          checked: false,
          tableData: [],
          currentPage: 1,
          pageSize: 10,
          total: 0
        },
        item5: {
          radio: '0',
          dateS: '2022-03-15 00:00:00',
          dateE: '2025-03-15 00:00:00',
          query: '选项1',
          checked: false,
          tableData: [],
          currentPage: 1,
          pageSize: 10,
          total: 0
        }
      },
      options: [
        {
          value: '选项1',
          label: '全部'
        },
        {
          value: '选项2',
          label: '胸外科'
        },
        {
          value: '选项3',
          label: '血液科'
        },
        {
          value: '选项4',
          label: '口腔科'
        }
      ]
    }
  },
  async mounted() {
    await getBaseInfo()
  },
  methods: {
    async handleClick(row) {
      this.dialogVisible = true
      console.log(row)
      let res = await approval({
        beiZhu: row.shenQingDanID,
        buMenID: row.shenQingDanID,
        shenPiJG: row.shenQingDanID,
        shenPiLB: row.shenQingDanID,
        shenPiZT: row.shenQingDanID,
        shenQingDanID: row.shenQingDanID,
        zhuanKeMC: row.shenQingDanID
      })
      console.log(res)
    },
    changeTabClick(tab) {
      const prevTab = this.activeName
      const currentTab = tab.name
      // 保存当前标签页状态（例如持久化到 localStorage）
      localStorage.setItem(prevTab, JSON.stringify(this.tabStates[prevTab]))
      // 加载新标签页状态
      this.activeName = currentTab
      // if (!this.tabStates[currentTab].tableData.length) {
      //   this.searchFun(); // 首次切换时加载数据
      // }
    },
    handleDelDate(dateStr) {
      // 提取年月日时分秒
      const date = new Date(dateStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      return formattedDate
    },
    onChangeDate(e) {
      if (e) {
        this.tabStates[this.activeName].dateS = e[0]
        this.tabStates[this.activeName].dateE = e[1]
      }
    },
    handlePageChange(val, type) {
      this.tabStates[this.activeName][type] = val
      this.searchFun()
    },
    async searchFun() {
      const currentTab = this.activeName
      let res = ''
      if (this.activeName === 'item1') {
        res = await getApprovalFromShangJiYiShi({
          zhuanKeID: this.$store.state.patient.initInfo.zhuanKeID,
          shenPiZT: this.tabStates[currentTab].radio,
          isPage: true,
          pageSize: this.tabStates[currentTab].pageSize,
          pageNum: this.tabStates[currentTab].currentPage,
          yuBenRenYG: this.tabStates[currentTab].checked ? 1 : 0,
          kaiShiSJ: this.tabStates[currentTab].dateS,
          jieShuSJ: this.tabStates[currentTab].dateE
        })
      } else if (this.activeName === 'item2') {
        res = await getApprovalFromKeShi({
          zhuanKeID: this.$store.state.patient.initInfo.zhuanKeID,
          shenPiZT: this.tabStates[currentTab].radio,
          isPage: true,
          pageSize: this.tabStates[currentTab].pageSize,
          pageNum: this.tabStates[currentTab].currentPage,
          yuBenRenYG: this.tabStates[currentTab].checked ? 1 : 0,
          kaiShiSJ: this.tabStates[currentTab].dateS,
          jieShuSJ: this.tabStates[currentTab].dateE
        })
      } else if (this.activeName === 'item3') {
        res = await getApprovalFromYiWuKe({
          zhuanKeID: this.$store.state.patient.initInfo.zhuanKeID,
          shenPiZT: this.tabStates[currentTab].radio,
          isPage: true,
          pageSize: this.tabStates[currentTab].pageSize,
          pageNum: this.tabStates[currentTab].currentPage,
          yuBenRenYG: this.tabStates[currentTab].checked ? 1 : 0,
          kaiShiSJ: this.tabStates[currentTab].dateS,
          jieShuSJ: this.tabStates[currentTab].dateE
        })
      } else if (this.activeName === 'item4') {
        res = await getApprovalWithoutSP({
          zhuanKeID: this.$store.state.patient.initInfo.zhuanKeID,
          shenPiZT: this.tabStates[currentTab].radio,
          isPage: true,
          pageSize: this.tabStates[currentTab].pageSize,
          pageNum: this.tabStates[currentTab].currentPage,
          yuBenRenYG: this.tabStates[currentTab].checked ? 1 : 0,
          kaiShiSJ: this.tabStates[currentTab].dateS,
          jieShuSJ: this.tabStates[currentTab].dateE
        })
      } else if (this.activeName === 'item5') {
        res = await getApprovalAll({
          zhuanKeID: this.$store.state.patient.initInfo.zhuanKeID,
          shenPiZT: this.tabStates[currentTab].radio,
          isPage: true,
          pageSize: this.tabStates[currentTab].pageSize,
          pageNum: this.tabStates[currentTab].currentPage,
          yuBenRenYG: this.tabStates[currentTab].checked ? 1 : 0,
          kaiShiSJ: this.tabStates[currentTab].dateS,
          jieShuSJ: this.tabStates[currentTab].dateE
        })
      }
      if (res.errorMessage === '请求成功') {
        this.tabStates[currentTab].tableData = this.handleDelArr(res.data.jiLuLB)
        // this.handleDelArr(res.data.jiLuLB)
        this.tabStates[currentTab].total = res.data.jiLuZS
      }
    },
    handleDelArr(data) {
      return data.map((item) => ({
        ...item,
        leiBie: item.leiBie === 0 ? '门诊' : item.leiBie === 1 ? '住院' : '急诊'
      }))
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  // background-color: #fff;
  background-color: #eff3fb;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  // margin-bottom: 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  margin: 12px 0 5px;
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    // margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eff3fb;
  height: 730px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 1476px;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}
.bottom {
  background: #eaf0f9;
  margin-top: 10px;
  padding: 15px 20px;
  .title-tips {
    color: #171c28;
    font-size: 14px;
    font-weight: bold;
    margin-left: 5px;
  }
  .tips {
    color: #171c28;
    line-height: 25px;
    padding-left: 15px;
  }
}
::v-deep .is-active {
  color: #356ac5 !important;
  background: #eff3fb !important;
  font-weight: bold;
}
::v-deep .el-tabs__item {
  background: #fff;
}
::v-deep .el-radio__label {
  padding-left: 6px;
}
::v-deep .el-pagination {
  margin-top: 8px;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination .number,
::v-deep .el-pagination .el-input__inner,
::v-deep .el-pager li.btn-quicknext,
.el-pager li.btn-quickprev {
  background: #fff !important;
  font-weight: 100 !important;
}
</style>
