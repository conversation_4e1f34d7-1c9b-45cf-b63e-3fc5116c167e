<template>
  <div>
    <el-table :data="tableData" border stripe size="mini">
      <el-table-column prop="chuangWeiHao" label="床位号"></el-table-column>
      <el-table-column prop="shouShuXM" label="手术名称">
        <template slot-scope="scope">
          <div class="shou-shu-mc">
            <span v-for="(shouShu, index) in scope.row.shouShuXM" :key="index">
              {{ shouShu.shouShuMC }}
            </span>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getShouShuListByZhuanKeID } from '@/api/patient'
import fetchData from '@/views/patient-home/components/mixin/fetchData'

export default {
  name: 'TodaySurgeryPatients',
  mixins: [fetchData],
  data() {
    return {
      tableData: []
    }
  },
  methods: {
    fetchData() {
      getShouShuListByZhuanKeID({ zhuanKeID: this.zhuanKeID }).then((res) => {
        if (res.hasError === 0) {
          this.tableData = res.data.data
          this.$emit('update:count', this.tableData.length)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.shou-shu-mc {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
