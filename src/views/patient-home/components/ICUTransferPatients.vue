<template>
  <div class="icu-zhuan-chu">
    <el-table :data="tableData" border stripe size="mini">
      <el-table-column prop="bingRenXM" label="病人姓名"></el-table-column>
      <el-table-column prop="zhuYuanID" label="病案号"></el-table-column>
      <el-table-column prop="chuangWei" label="病区-床位">
        <template slot-scope="scope">
          {{
            scope.row.zhuanKeMC && scope.row.chuangWeiHao
              ? `${scope.row.zhuanKeMC}-${scope.row.chuangWeiHao}`
              : ''
          }}
        </template>
      </el-table-column>
    </el-table>
    <div class="tz">病人病情已稳定，请于12小时内转回本专科，逾期扣罚款绩效500/例</div>
  </div>
</template>

<script>
import { getIcuTransferCount } from '@/api/patient'
import fetchData from '@/views/patient-home/components/mixin/fetchData'

export default {
  name: 'ICUTransferPatients',
  mixins: [fetchData],
  data() {
    return {
      tableData: []
    }
  },
  methods: {
    fetchData() {
      getIcuTransferCount({ zhuanKeID: this.zhuanKeID, zhiLiaoZuID: this.zhiLiaoZuID }).then(
        (res) => {
          if (res.hasError === 0) {
            this.tableData = res.data
            this.$emit('update:count', res.data.length)
          }
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.icu-zhuan-chu {
  .tz {
    padding: 6px 20px;
  }
}
</style>
