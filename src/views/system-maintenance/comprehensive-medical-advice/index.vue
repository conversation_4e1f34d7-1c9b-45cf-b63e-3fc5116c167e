<template>
  <div class="container">
    <div class="content">
      <div class="left">
        <div class="left-button-group">
          <el-button type="primary" @click="handleAdd">新建</el-button>
          <el-button type="primary">修改</el-button>
          <el-button type="primary">删除</el-button>
        </div>
        <div class="model-group">
          <el-collapse accordion>
            <template v-for="(item, index) in muBanData">
              <div :key="item.code" class="model-item">
                <el-collapse-item :title="item.name" :name="index">
                  <template v-for="(childItem, cIndex) in item.children">
                    <div :key="cIndex" class="model-item-child" @click="onMuBanClick(childItem)">
                      {{ childItem.mingCheng }}
                    </div>
                  </template>
                </el-collapse-item>
              </div>
            </template>
          </el-collapse>
        </div>
      </div>
      <div v-if="muBanTitle != ''" class="right">
        <div class="right-header">
          <div class="right-header-bar">
            <div class="right-header-title">模板明细 ：</div>
            <el-radio v-model="hospitalArea" label="0">老院区</el-radio>
            <el-radio v-model="hospitalArea" label="1">新院区</el-radio>
          </div>
          <div class="right-button-group">
            <el-button type="primary">保存</el-button>
            <el-button type="primary">新增组</el-button>
            <el-button type="primary">新增项</el-button>
            <el-button type="primary">插入项</el-button>
            <el-button type="primary">新增组</el-button>
            <el-button type="primary">删除组</el-button>
            <el-button type="primary">删除项</el-button>
            <el-button type="primary">独立成组</el-button>
          </div>
        </div>
        <div class="right-list-title">{{ muBanTitle }}</div>
        <div class="right-table">
          <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="xuHao" label="序号"></el-table-column>
            <el-table-column label="类别">
              <template slot-scope="scope">
                {{ leiBieMap(scope.row.leiBie) }}
              </template>
            </el-table-column>
            <el-table-column prop="mingCheng" width="242" label="医嘱名称"></el-table-column>
            <el-table-column prop="zuHao" label="组"></el-table-column>
            <el-table-column prop="guiGe" label="规格/单价"></el-table-column>
            <el-table-column prop="yiCiYL" label="一次用量"></el-table-column>
            <el-table-column prop="jiLiangDW" label="计量单位"></el-table-column>
            <el-table-column prop="zhiXingFF" label="用法"></el-table-column>
            <el-table-column prop="zhiXingPL" label="频率"></el-table-column>
            <el-table-column prop="geiYaoSJ" label="用药/执行时间"></el-table-column>
            <el-table-column prop="teShuYF" label="医嘱说明/备注说明"></el-table-column>
            <el-table-column prop="chiXuTS" label="持续天数"></el-table-column>
            <el-table-column prop="yiCiYL" label="数量/剂数"></el-table-column>
            <el-table-column prop="jiLiangDW" label="单位"></el-table-column>
          </el-table>
        </div>
      </div>
      <el-dialog :visible.sync="dialogVisible" width="40%" :before-close="handleClose">
        <div slot="title" class="dialog-title">综合医嘱模板-新增</div>
        <div>
          <span>模板序号:</span>
          <el-input v-model="muBanXuHao"></el-input>
        </div>
        <div>
          <span>模板名称:</span>
          <el-input v-model="muBanMingCheng"></el-input>
        </div>
        <div class="muBanLX">
          <span>模板类型:</span>
          <el-radio v-model="muBanLeiBie" label="4">个人</el-radio>
          <el-radio v-model="muBanLeiBie" label="3">专科</el-radio>
          <el-radio v-model="muBanLeiBie" label="1">全院</el-radio>
        </div>
        <div class="bottom-btn-group">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  getZongHeYzMbMlByID,
  getZongHeYzMbMlByLb,
  getZongHeYzMbMx,
  saveZongHeYzMbMl,
  saveZongHeYzMbMx,
  zongHeYzMbInit
} from '@/api/system-maintenance'
export default {
  data() {
    return {
      tableData: [
        {
          xuHao: '1',
          leiBie: '药品',
          yiZhuMC: '(捷佰舒)注射用奈达铂',
          zu: '',
          guiGeDJ: '10mg',
          yiCiYL: '1',
          jiLiangDW: 'mg',
          yongFa: '肌肉注射',
          pingLv: '每小时1次',
          yongYaoZXSJ: '餐前',
          shuoMing: '',
          chiXuTS: '',
          shuLiangJS: '',
          danWei: '瓶'
        }
      ],
      hospitalArea: '0',
      muBanData: [],
      muBanTitle: '',
      dialogVisible: false,
      handleMode: '',
      muBanXuHao: '',
      muBanMingCheng: '',
      muBanLeiBie: '4'
    }
  },
  computed: {
    leiBieMap() {
      return (val) => {
        switch (val) {
          case '1':
            return '治疗'
          case '2':
            return '饮食'
          case '3':
            return '药品'
          case '4':
            return '嘱托'
          default:
            return 'null'
        }
      }
    }
  },
  async mounted() {
    await this.refreshData()
  },
  methods: {
    async refreshData() {
      let res = await zongHeYzMbInit()
      let muBanLB = res.data.muBanLB
      muBanLB.map(async (item) => {
        let mbml = await getZongHeYzMbMlByLb(item.code)
        item.children = mbml.data
        this.muBanData.push(item)
        if (this.muBanData.length == muBanLB.length) {
          this.muBanData.sort((a, b) => b.code - a.code)
        }
      })
      console.log(this.muBanData)
    },
    async onMuBanClick(item) {
      let mbmx = await getZongHeYzMbMx(item.moBanID)
      this.muBanTitle = item.mingCheng
      this.tableData = mbmx.data
      console.log(mbmx)
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleAdd() {
      this.handleMode = 'add'
      this.dialogVisible = true
      // this.mingCheng = ''
      // this.neiRong = ''
      // this.zhuangTaiBZ = ''
      // this.leiBie = ''
      // this.icdid = ''
      // this.moBanID = ''
      // this.caoZuoZheID = ''
    },
    async handleSave() {
      let addList = [
        {
          xuHao: this.muBanXuHao,
          mingCheng: this.muBanMingCheng,
          leiBie: this.muBanLeiBie
        }
      ]
      let res = await saveZongHeYzMbMl({ addList })
      console.log(res)
      console.log(123)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-button {
  height: 32px;
  border-radius: 4px;
}
::v-deep .el-button:hover {
  background-color: #3b76ef;
}
::v-deep .el-collapse-item__header::before {
  display: inline-block;
  width: 3px;
  height: var(--font-size-regular);
  margin-right: -webkit-calc(var(--common-margin) / 2);
  margin-right: -moz-calc(var(--common-margin) / 2);
  margin-right: calc(var(--common-margin) / 2);
  content: '';
  background-color: var(--color-primary);
}
.container {
  background-color: #fff;
}
.tag-bar {
  padding: 0px 10px;
}
.content {
  padding: 10px 70px;
  display: flex;
  .left {
    background-color: #eff3fb;
    // width: 382px;
    height: 800px;
    padding: 14px;
    overflow: auto;
    .left-button-group {
      width: 354px;
      height: 58px;
      margin-bottom: 14px;
      box-shadow: 0 0 0 0.08px black;
      border-radius: 4px;
      padding: 13px 14px;
    }
    .model-group {
      .model-item {
        width: 354px;
        box-shadow: 0 0 0 0.04px black;
        margin-bottom: 14px;
        .model-item-child {
          font-size: 14px;
          padding: 12px;
          user-select: none;
          border: 1px solid #dcdfe6;
        }
        .model-item-child:hover {
          background-color: #6787cc;
          color: #fff;
          cursor: pointer;
        }
        ::v-deep .el-collapse-item__content {
          padding: 12px;
          border: 1px solid #dcdfe6;
        }
      }
    }
  }
  .right {
    flex: 1;
    background-color: #eff3fb;
    // width: 1273px;
    height: 800px;
    margin-left: 10px;
    overflow: auto;
    padding: 14px;
    border-radius: 4px;
    .right-header {
      display: flex;
      align-items: center;
      margin-bottom: 14px;
      border: 1px solid #dcdfe6;
      padding: 10px 6px;
      .right-header-bar {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .right-header-title {
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        margin-left: 9px;
      }
      .right-button-group {
        margin-left: 27px;
      }
    }
    .right-list-title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
      margin-bottom: 14px;
    }
    .right-list-title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }
}
::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
td {
  border: 1px solid #ddd;
  border-collapse: collapse; /* 移除表格内边框间的间隙 */
  height: 35px;
  padding: 10px;
}
.info-label {
  text-align: right;
  width: 10vw;
  background-color: #eaf0f9;
}
.info-value {
  width: 14vw;
  height: 30px;
  background-color: #f7f9fd;
}
.flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.no-padding {
  padding: 0;
  .no-padding-label {
    padding: 10px;
  }
  ::v-deep .el-button {
    flex: 1;
    height: 100%;
  }
}
.bottom-btn-group {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 30px;
  ::v-deep .el-button {
    margin-left: 10px;
  }
}
.muBanLX {
  display: flex;
  align-items: center;
  span {
    margin-right: 20px;
  }
}
</style>
