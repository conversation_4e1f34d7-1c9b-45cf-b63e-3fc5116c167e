<template>
  <div class="container">
    <div class="header">
      <div class="flex" style="width: 220px">
        <el-radio v-model="select" label="0">住院号:</el-radio>
        <el-input v-model="zhuYuanHao"></el-input>
      </div>
      <div class="flex" style="width: 275px">
        <el-radio v-model="select" label="1">病案号(empi):</el-radio>
        <el-input v-model="bingAnHao"></el-input>
      </div>
      <div class="flex" style="width: 350px">
        <el-radio v-model="select" label="2">时间范围:</el-radio>
        <el-date-picker
          v-model="date"
          type="daterange"
          range-separator="至"
          start-placeholde="开始日期"
          end-placeholde="结束日期"
          @change="onDateChange"
        ></el-date-picker>
      </div>
      <div class="flex" style="width: 220px">
        <el-select v-model="filingVal">
          <el-option
            v-for="item in filingOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button>查询</el-button>
      </div>
      <div style="width: 180px">
        <el-alert
          type="warning"
          description="未归档显示所有"
          :closable="false"
          show-icon
        ></el-alert>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">医生归档病历查询</div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData" style="width: fit-content">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column prop="bingAnHao" label="病案号" width="119"></el-table-column>
          <el-table-column prop="xingMing" label="姓名" width="128"></el-table-column>
          <el-table-column prop="xingBie" label="性别" width="88"></el-table-column>
          <el-table-column prop="zhuanKe" label="专科" width="188"></el-table-column>
          <el-table-column prop="ruYuanRQ" label="入院日期" width="188"></el-table-column>
          <el-table-column prop="chuYuanRQ" label="出院日期" width="188"></el-table-column>
          <el-table-column prop="tiJiaoYS" label="提交医生" width="128"></el-table-column>
          <el-table-column prop="tiJiaoHS" label="提交护士" width="204"></el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button @click="onHandlerClick(scope.row)">查看病人</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      select: '0',
      zhuYuanHao: '',
      bingAnHao: '',
      date: ['2025-03-04', '2025-04-14'],
      filingOption: [
        {
          label: '医生未归档',
          value: '0'
        },
        {
          label: '医生已归档',
          value: '1'
        }
      ],
      filingVal: '0',
      tableData: [
        {
          bingAnHao: '0015094563',
          xingMing: '戴雅',
          xingBie: '女',
          zhuanKe: '风湿免疫科',
          ruYuanRQ: '2023-07-12',
          chuYuanRQ: '2023-07-13',
          tiJiaoYS: '',
          tiJiaoHS: ''
        }
      ]
    }
  },
  methods: {
    onDateChange(val) {
      console.log(val, this.date)
    }
  }
}
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  margin-right: 20px;
}
.container {
  background-color: #fff;
  padding: 5px 70px;
  .header {
    padding: 16px;
    display: flex;
    align-items: center;
    background-color: #eaf0f9;
    ::v-deep .el-button {
      background-color: #a66dd4;
      color: #fff;
    }
    ::v-deep .el-alert {
      background-color: rgba($color: #000000, $alpha: 0);
      border: 1px solid rgba($color: #155bd4, $alpha: 0.45);
      color: #356ac5;
      padding: 6px;
    }
    ::v-deep .el-alert__description {
      color: #000;
    }
  }
  .content {
    background-color: #eaf0f9;
    padding: 14px;
    min-height: 700px;
    .table {
      ::v-deep .el-button {
        border: none;
        color: #356ac5;
      }
    }
  }
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 992px;
    margin-bottom: 14px;
  }
  .title {
    position: relative;
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 9px;
  }
  .title::before {
    position: absolute;
    left: -9px;
    width: 3px;
    height: 14px;
    content: '';
    background-color: #356ac5;
  }
}
</style>
