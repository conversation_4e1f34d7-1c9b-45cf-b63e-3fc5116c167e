<template>
  <div class="container">
    <div class="left">
      <div class="left-header">
        <div class="left-header-title">营养套餐模板</div>
        <el-input v-model="muBanMC"></el-input>
        <el-button @click="addTc">新增</el-button>
      </div>
      <div class="left-content">
        <div class="left-content-title">模板名称</div>
        <template v-for="(item, index) in mcList">
          <div :key="index" class="left-content-item" @click="mcClick(item)">
            {{ item }}
          </div>
        </template>
      </div>
    </div>
    <div class="right">
      <div class="right-header">
        <div class="right-header-title">{{ nowMc }}</div>
        <div>
          <el-button class="right-header-btn" @click="handleClick">内容新增</el-button>
        </div>
      </div>
      <div class="right-content">
        <el-table max-height="648" border :data="tableData">
          <el-table-column prop="yaoPinMC" label="名称"></el-table-column>
          <el-table-column prop="yiCiYL" label="用量"></el-table-column>
          <el-table-column prop="zuHeHao" label="组合"></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button @click="upClick(scope.row)">编辑</el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button @click="handleDel(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-dialog :visible.sync="dialogVisible" width="40%" :before-close="handleClose">
        <div slot="title" class="dialog-title">
          <i class="el-icon-coin"></i>
          <span style="margin-left: 5px">明细-{{ dialogType }}</span>
        </div>
        <div>
          <el-form ref="nowRow" :model="nowRow" label-width="80px">
            <table>
              <tr>
                <td class="info-label">药品名称：</td>
                <td class="info-value" colspan="3">
                  <el-input v-model="nowRow.yaoPinMC" @focus="ypClick"></el-input>
                </td>
              </tr>
              <tr>
                <td class="info-label">用量：</td>
                <td class="info-value">
                  <div style="display: flex; justify-content: space-between">
                    <el-input v-model="nowRow.yiCiYL"></el-input>
                    <div style="margin: 2px; width: 20px; display: flex; align-items: center">
                      ml
                    </div>
                  </div>

                  <!--                    {{nowRow.jiLiangDW}}-->
                  <!--                    <el-select v-model="nowRow.jiLiangDW">-->
                  <!--                      <el-option label="u" value="u"/>-->
                  <!--                      <el-option label="ml" value="ml"/>-->
                  <!--                      <el-option label="支" value="支"/>-->
                  <!--                    </el-select>-->
                </td>
                <td class="info-label">组合：</td>
                <td class="info-value">
                  <el-select v-model="nowRow.zuHeHao" style="width: 100%">
                    <el-option label="必选" value="0" />
                    <el-option label="组合1" value="1" />
                    <el-option label="组合2" value="2" />
                    <el-option label="组合3" value="3" />
                  </el-select>
                </td>
              </tr>
              <tr>
                <td class="info-label">排序：</td>
                <td class="info-value">
                  <el-input v-model="nowRow.paiXu"></el-input>
                </td>
                <td class="info-label">状态标志：</td>
                <td class="info-value">
                  <el-radio-group v-model="nowRow.zhuangTaiBZ" class="ml-4">
                    <el-radio label="1" size="large">启用</el-radio>
                    <el-radio label="0" size="large">停用</el-radio>
                  </el-radio-group>
                </td>
              </tr>
              <tr>
                <td class="info-label">备注：</td>
                <td class="info-value" colspan="3">
                  <el-input
                    v-model="nowRow.beiZhu"
                    type="textarea"
                    :rows="7"
                    show-word-limit
                    maxlength="1000"
                  />
                </td>
              </tr>
            </table>
          </el-form>
        </div>
        <div class="bottom-btn-group">
          <el-button @click="handleClose">关闭</el-button>
          <el-button v-if="dialogType === '新增'" type="primary" @click="handleSave">
            保存
          </el-button>
          <el-button v-else type="primary" @click="handleUpdate">保存</el-button>
        </div>
      </el-dialog>

      <el-dialog :visible.sync="ypVisible" width="30%" :before-close="ypClose">
        <div slot="title" class="dialog-title">
          <i class="el-icon-coin"></i>
          <span style="margin-left: 5px">选择药品</span>
        </div>
        <div class="yp-div">
          <el-form>
            <el-form-item label="药品查询:">
              <el-input v-model="pinYin" placeholder="请输入拼音码过滤" @input="ypClick" />
            </el-form-item>
          </el-form>
          <el-table max-height="648" border :data="ypData">
            <el-table-column prop="yaoPinMC" label="药品名称" width="300"></el-table-column>
            <el-table-column prop="jiLiang" label="规格" width="130"></el-table-column>
            <el-table-column label="选择" align="center">
              <template slot-scope="scope">
                <el-button @click="xzYaoPin(scope.row)">选择</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            :current-page="currentPage"
            :page-size="pageSize"
            background
            layout="total, prev, pager, next"
            :total="total"
            @current-change="pageClick"
          ></el-pagination>

          <div class="bottom-btn-group">
            <el-button type="primary" @click="ypClose">确认</el-button>
            <el-button @click="ypClose">关闭</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import {
  addYlWcyytcList,
  deleteYlWcyytcListByIds,
  getYingYangYPVoListByPage,
  getYlWcyytcAllTaoCanMC,
  getYlWcyytcDMCVoList,
  getZhiLiaoMbMlByLb,
  updateYlWcyytc,
  zhiLiaoMbInit
} from '@/api/system-maintenance'
import ElMessageBox from 'dropzone'

export default {
  data() {
    return {
      mcList: [],
      nowMc: '',
      tableData: [],
      muBanMC: '',
      dialogType: '',
      dialogVisible: false,
      ypVisible: false,
      nowRow: {
        taoCanMC: '',
        yaoPinMC: '',
        yaoPinID: '',
        yiCiYL: '',
        jiLiangDW: 'ml',
        zuHeHao: '',
        paiXu: '',
        zhuangTaiBZ: '',
        beiZhu: ''
      },
      pinYin: '',
      ypData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  async mounted() {
    await this.getTaoCanMC()
  },
  methods: {
    async getTaoCanMC() {
      let mcData = await getYlWcyytcAllTaoCanMC()
      this.mcList = mcData.data
      if (this.mcList.length > 0) {
        await this.mcClick(this.mcList[0])
      }
    },
    async mcClick(row) {
      if (row) {
        this.nowMc = row
      }
      let tcData = await getYlWcyytcDMCVoList({ taoCanMC: this.nowMc })
      this.tableData = tcData.data
    },
    handleClick() {
      this.dialogType = '新增'
      this.dialogVisible = true
    },
    upClick(row) {
      this.dialogType = '编辑'
      this.dialogVisible = true
      this.nowRow = { ...row }
    },
    async addTc() {
      if (this.muBanMC.length <= 0) {
        this.$message({
          message: '请输入模板名称！'
        })
        return
      }
      const res = await addYlWcyytcList([{ taoCanMC: this.muBanMC }])
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: '新增成功！',
          type: 'success'
        })
        await this.getTaoCanMC()
      }
    },
    async handleSave() {
      this.nowRow.taoCanMC = this.nowMc
      const res = await addYlWcyytcList([this.nowRow])
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: '新增成功！',
          type: 'success'
        })
        this.handleClose()
        await this.mcClick()
      }
    },
    async handleUpdate() {
      this.nowRow.taoCanMC = this.nowMc
      const res = await updateYlWcyytc([this.nowRow])
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: '更新成功！',
          type: 'success'
        })
        this.handleClose()
        await this.mcClick()
      }
    },
    async handleDel(row) {
      this.$confirm(`确认删除${row.yaoPinMC}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          console.log('进入删除')
          const res = await deleteYlWcyytcListByIds({ list: row.id })
          console.log(res)
          if (res.hasError === -1) {
            this.$message.error(res.errorMessage)
          } else {
            this.$message({
              message: '删除成功！',
              type: 'success'
            })
            await this.mcClick()
          }
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        })
    },
    handleClose() {
      this.dialogVisible = false
      this.nowRow = {}
    },
    ypClose() {
      this.ypVisible = false
      this.pinYin = ''
    },
    ypClick() {
      this.ypVisible = true
      this.currentPage = 1
      this.getYp()
    },
    async getYp() {
      let data = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize
      }
      if (this.pinYin && this.pinYin.length > 0) {
        data.pinYin = this.pinYin
      }
      let ypData = await getYingYangYPVoListByPage(data)
      this.ypData = ypData.data.records
      this.total = ypData.data.total
    },
    pageClick(ys) {
      this.currentPage = ys
      this.getYp()
    },
    xzYaoPin(row) {
      this.nowRow.yaoPinMC = row.yaoPinMC
      this.nowRow.yaoPinID = row.yaoPinID
      this.ypClose()
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 10px 80px;
  display: flex;
  background-color: #fff;
  .left {
    margin-right: 20px;
    width: 20%;
    border-radius: 4px;
    background: #eaf0f9;
    padding: 5px 16px;
    .left-header {
      margin-left: 9px;
      margin-top: 10px;
      display: flex;
      align-items: center;
      .left-header-title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        width: 200px;
      }
      .left-header-title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }
      ::v-deep .el-button {
        background-color: #3b76ef;
        color: #fff;
      }
    }
    .left-content {
      margin-top: 16px;
      font-size: 14px;
      line-height: 14px;
      color: #171c28;
      .left-content-title {
        padding: 9px 12px;
        background-color: #eaf0f9;
        border: 1px solid #dcdfe6;
      }
      .left-content-item {
        padding: 9px 12px;
        border: 1px solid #dcdfe6;
      }
      .left-content-item:nth-child(odd) {
        background-color: #eff3fb;
      }
      .left-content-item:nth-child(even) {
        background-color: #f6f6f6;
      }
      .left-content-item:hover {
        background-color: #6787cc;
        color: #fff;
      }
    }
  }
  .right {
    background-color: #eff3fb;
    padding: 10px 12px;
    width: 80%;
    .right-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 70%;
      .right-header-title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        margin-left: 9px;
      }
      .right-header-title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }
      ::v-deep .el-button {
        background-color: #3b76ef;
        color: #fff;
      }
    }
    .right-content {
      width: 70%;
      margin-top: 20px;
      ::v-deep .el-button {
        color: #356ac5;
        border: none;
        margin: 0;
      }
    }
  }
}

td {
  border: 1px solid #ddd;
  border-collapse: collapse; /* 移除表格内边框间的间隙 */
  height: 35px;
  padding: 10px;
}
.info-label {
  padding: 3px;
  text-align: right;
  width: 130px;
  background-color: #eaf0f9;
}
.info-value {
  padding: 3px;
  width: 14vw;
}
.bottom-btn-group {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 30px;
  ::v-deep .el-button {
    margin-left: 10px;
  }
}
.ml-4 {
  margin-left: 8px;
}

.yp-div {
  padding: 0 20px 10px 20px;

  ::v-deep .el-table .el-table__cell {
    padding: 5px 0;
  }
}
</style>
