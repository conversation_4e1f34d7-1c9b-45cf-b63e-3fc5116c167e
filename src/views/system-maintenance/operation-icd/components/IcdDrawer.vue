<template>
  <el-dialog :visible="visible" width="700px" @open="initFormData()" @close="updateVisible(false)">
    <span slot="title">
      <span class="drawer-title">
        <i class="el-icon-menu"></i>
        手术ICD维护-编辑
      </span>
    </span>
    <span>
      <div class="drawer-component">
        <table v-if="formData">
          <tbody>
            <tr>
              <td class="info-label">
                <span>*</span>
                是否别名:
              </td>
              <td class="info-value">
                <el-radio v-model="formData.shiFouBM" label="1">是</el-radio>
                <el-radio v-model="formData.shiFouBM" label="0">否</el-radio>
              </td>
              <td class="info-label">
                <span>*</span>
                手术名称:
              </td>
              <td class="info-value">
                <el-input v-model="formData.shouShuMC" />
              </td>
            </tr>
            <tr>
              <td class="info-label">
                <span>*</span>
                ICD代码:
              </td>
              <td class="info-value">
                <el-input v-model="formData.icddm" />
              </td>
              <td class="info-label">
                <span>*</span>
                手术级别:
              </td>
              <td class="info-value">
                <el-select v-model="formData.shouShuJB" placeholder="">
                  <el-option
                    v-for="item in [{ value: 0, label: '一级' }]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </td>
            </tr>
            <tr>
              <td class="info-label">
                <span>*</span>
                切口类型:
              </td>
              <td class="info-value">
                <el-select v-model="formData.qieKouLB" placeholder="">
                  <el-option
                    v-for="item in [{ value: 'test', label: '测试选项' }]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </td>
              <td class="info-label">
                <span>*</span>
                手术类别:
              </td>
              <td class="info-value">
                <el-select v-model="formData.shouShuLB" placeholder="">
                  <el-option
                    v-for="item in [{ value: 0, label: '治疗性操作' }]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </td>
            </tr>
            <tr>
              <td class="info-label">
                <span>*</span>
                一级分类:
              </td>
              <td class="info-value">
                <el-select v-model="formData.yiJiFL" placeholder="">
                  <el-option
                    v-for="item in [{ value: 0, label: '原位肿瘤' }]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </td>
              <td class="info-label">
                <span>*</span>
                二级分类:
              </td>
              <td class="info-value">
                <el-select v-model="formData.erJiFL" placeholder="">
                  <el-option
                    v-for="item in [{ value: 0, label: '测试选项' }]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </td>
            </tr>
            <tr>
              <td class="info-label">
                <span>*</span>
                状态标志:
              </td>
              <td class="info-value">
                <el-radio v-model="formData.zhuangTaiBZ" label="1">启用</el-radio>
                <el-radio v-model="formData.zhuangTaiBZ" label="0">停用</el-radio>
              </td>
              <td class="info-label">
                <span>*</span>
                手术属性:
              </td>
              <td class="info-value">
                <el-input v-model="formData.shouShuSX" />
              </td>
            </tr>
            <tr>
              <td class="info-label">
                <span>*</span>
                所属系统:
              </td>
              <td class="info-value">
                <el-input v-model="formData.suoShuXT" />
              </td>
              <td class="info-label">
                <span>*</span>
                是否必填:
              </td>
              <td class="info-value">
                <el-radio v-model="formData.shiFouBT" label="1">是</el-radio>
                <el-radio v-model="formData.shiFouBT" label="0">否</el-radio>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="uploadForm()">保 存</el-button>
      <el-button @click="updateVisible(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils'
import { saveYlSsicd } from '@/api/system-maintenance'

export default {
  name: 'IcdDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    icdData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      notes: '',
      formData: null
    }
  },
  methods: {
    uploadForm() {
      saveYlSsicd(this.formData).then((res) => {
        if (res.hasError === 0) {
          console.log('saveYlSsicd res:', res)
          this.updateVisible(false)
          this.$emit('upload-success')
          this.$message({
            message: '保存成功',
            type: 'success',
            duration: 700
          })
        }
      })
    },
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    initFormData() {
      this.formData = deepClone(this.icdData)
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-title {
  font-size: 16px;
}

.drawer-component {
  padding: 0px 16px;
  table {
    width: 100%;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
    padding: 5px;
  }
  .info-label {
    text-align: right;
    width: 110px;
    background-color: #eaf0f9;
    span {
      color: #f35656;
      position: relative;
      top: 3px;
      right: 3px;
    }
  }
  .info-value {
    width: 230px;
    background-color: #ffffff;
    a {
      text-decoration: underline;
      color: #356ac5;
    }
  }
}
:deep(.el-dialog__footer) {
  border-top: none;
}

:deep(.el-radio) input[aria-hidden='true'] {
  display: none !important;
}
</style>
