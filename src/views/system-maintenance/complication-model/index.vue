<template>
  <div class="container">
    <div class="header">
      <div class="query-word">诊断名称：</div>
      <div>
        <el-input v-model="query"></el-input>
      </div>
      <div class="button">
        <el-button type="primary">查询</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">并发症模板维护</div>
        <div class="button"><el-button type="primary">新增</el-button></div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData" style="width: 734px">
          <el-table-column prop="xuHao" width="148" label="序号"></el-table-column>
          <el-table-column prop="JBID" width="127" label="JBID"></el-table-column>
          <el-table-column prop="ICD" width="127" label="ICD"></el-table-column>
          <el-table-column prop="zhenDuanMC" width="228" label="诊断名称"></el-table-column>
          <el-table-column prop="caoZuo" width="104" label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      query: '',
      tableData: [
        {
          xuHao: '1',
          JBID: '10957',
          ICD: 'A92.100',
          zhenDuanMC: '奥尼昂-尼昂热',
          caoZuo: 0
        }
      ]
    }
  },
  methods: {
    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 700px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 734px;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}
</style>
