<template>
  <div class="container">
    <div class="left">
      <div class="left-header">
        <div class="left-header-title">专科名称</div>
        <el-input v-model="muBanMC" placeholder="请输入专科名称"></el-input>
        <el-button>查询</el-button>
      </div>
      <div class="left-content">
        <div class="left-content-title">列表名称</div>
        <div class="left-content-item">全部专科</div>
        <div class="left-content-item">401病区</div>
      </div>
    </div>
    <div class="right">
      <div class="right-header">
        <div class="right-header-title">401病区 已关联模板</div>
        <div>
          <el-button class="right-header-btn">关联维护</el-button>
        </div>
      </div>
      <div class="right-content">
        <el-table max-height="648" border :data="tableData">
          <el-table-column prop="keShiMC" label="科室名称"></el-table-column>
          <el-table-column prop="geShiMC" label="格式名称" width="260"></el-table-column>
          <el-table-column prop="beiZhu" label="备注" width="150"></el-table-column>
          <el-table-column prop="shiYong" label="使用"></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button @click="handleClick(scope.row)">关联维护</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tableData: [
        {
          keShiMC: 'EICU',
          geShiMC: '52:护理操作风险告知书（手写板用）',
          beiZhu: '	护理操作风险告知书',
          shiYong: '护'
        }
      ],
      muBanMC: ''
    }
  },
  methods: {
    handleClick() {}
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 10px 80px;
  display: flex;
  background-color: #fff;
  .left {
    margin-right: 20px;
    width: 25%;
    border-radius: 4px;
    background: #eaf0f9;
    padding: 5px 16px;
    .left-header {
      margin-left: 9px;
      margin-top: 10px;
      display: flex;
      align-items: center;
      .left-header-title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        width: 200px;
      }
      .left-header-title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }
      ::v-deep .el-button {
        background-color: #a66dd4;
        color: #fff;
      }
    }
    .left-content {
      margin-top: 16px;
      font-size: 14px;
      line-height: 14px;
      color: #171c28;
      .left-content-title {
        padding: 9px 12px;
        background-color: #eaf0f9;
        border: 1px solid #dcdfe6;
      }
      .left-content-item {
        padding: 9px 12px;
        border: 1px solid #dcdfe6;
      }
      .left-content-item:nth-child(odd) {
        background-color: #eff3fb;
      }
      .left-content-item:nth-child(even) {
        background-color: #f6f6f6;
      }
      .left-content-item:hover {
        background-color: #6787cc;
        color: #fff;
      }
    }
  }
  .right {
    background-color: #eff3fb;
    padding: 10px 12px;
    width: 75%;
    .right-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 70%;
      .right-header-title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        margin-left: 9px;
      }
      .right-header-title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }
      ::v-deep .el-button {
        background-color: #3b76ef;
        color: #fff;
      }
    }
    .right-content {
      width: 70%;
      margin-top: 20px;
      ::v-deep .el-button {
        color: #356ac5;
        border: none;
        margin: 0;
      }
    }
  }
}
</style>
