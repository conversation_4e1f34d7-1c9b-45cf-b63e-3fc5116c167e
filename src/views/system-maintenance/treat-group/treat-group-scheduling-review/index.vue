<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <div class="header-item-title">选择专科:</div>
        <div>
          <el-select v-model="zhuanKeMC" placeholder="请选择专科" size="small">
            <el-option
              v-for="item in zhuanKeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-button"><el-button>查询</el-button></div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">治疗组排班审核</div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData">
          <el-table-column prop="xingMing" label="姓名"></el-table-column>
          <el-table-column prop="zhiLiaoZu" label="治疗组"></el-table-column>
          <el-table-column prop="zhuanKeMC" label="专科"></el-table-column>
          <el-table-column prop="zhuanKeSX" label="专科属性"></el-table-column>
          <el-table-column prop="paiBanYF" label="排班月份"></el-table-column>
          <el-table-column prop="renYuanLB" label="人员类别"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" label="状态标志"></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button @click="onHandlerClick(scope.row)">同意</el-button>
              <el-button>不同意</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      zhuanKeMC: '',
      zhuanKeOptions: [
        {
          value: '1',
          label: '消化内科'
        }
      ],
      tableData: [
        {
          xingMing: '魏怜恤',
          zhiLiaoZu: '测试公共组',
          zhuanKeMC: '信息专用',
          zhuanKeSX: '',
          paiBanYF: '2021-09',
          renYuanLB: '组长',
          zhuangTaiBZ: '未审核'
        }
      ]
    }
  },
  methods: {
    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 720px;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
