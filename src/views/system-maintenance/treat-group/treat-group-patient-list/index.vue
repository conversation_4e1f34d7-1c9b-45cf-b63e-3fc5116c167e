<template>
  <div class="container">
    <div class="content">
      <div class="content-header">
        <div class="title">病人列表</div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData" style="width: fit-content">
          <el-table-column prop="bingAnHao" width="100" label="病案号"></el-table-column>
          <el-table-column prop="xingMing" label="姓名"></el-table-column>
          <el-table-column prop="chuangWeiHao" label="床位号"></el-table-column>
          <el-table-column prop="ruYuanSJ" width="100" label="入院时间"></el-table-column>
          <el-table-column prop="ruYuanDanZD" width="150" label="入院单诊断"></el-table-column>
          <el-table-column prop="lianXiDH" width="100" label="联系电话"></el-table-column>
          <el-table-column prop="kaiDanYS" label="开单医生"></el-table-column>
          <el-table-column prop="caoZuoYS" label="操作医生"></el-table-column>
          <el-table-column prop="bingRenYS" label="病人饮食"></el-table-column>
          <el-table-column prop="huLiJB" label="护理级别"></el-table-column>
          <el-table-column prop="zhiLiaoZu" width="150" label="治疗组"></el-table-column>
          <el-table-column prop="zhuGuanYS" label="主管医生"></el-table-column>
          <el-table-column width="100" label="状态" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.zhuangTai == 0">出院</el-tag>
              <el-tag v-else-if="scope.row.zhuangTai == 1" type="danger">在院</el-tag>
            </template>
          </el-table-column>
          <el-table-column width="100" label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tableData: [
        {
          bingAnHao: '0014061927',
          xingMing: '陶少春',
          chuangWeiHao: '272-025',
          ruYuanSJ: '2023-06-26',
          ruYuanDanZD: '间质性肺病,皮肌炎',
          lianXiDH: '13385776553',
          kaiDanYS: '卢瑶',
          caoZuoYS: '郑军将',
          bingRenYS: '半流',
          huLiJB: '二级护理',
          zhiLiaoZu: '呼吸内科蔡畅组',
          zhuGuanYS: '蔡畅',
          zhuangTai: 0
        }
      ]
    }
  },
  methods: {
    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 16px 70px;
  z-index: 999;
}
.content {
  background-color: #eaf0f9;
  height: 780px;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
