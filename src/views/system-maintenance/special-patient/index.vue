<template>
  <div class="container">
    <div class="header">
      <div style="display: flex; align-items: center">
        <el-radio v-model="selectType" label="姓名">姓名</el-radio>
        <el-radio v-model="selectType" label="病案号">病案号:</el-radio>
        <el-input v-model="selectContent"></el-input>
        <el-button>查询</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">特殊病人维护</div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData">
          <el-table-column prop="zhuanKe" label="专科" width="80"></el-table-column>
          <el-table-column prop="bingAnHao" label="病案号" width="120"></el-table-column>
          <el-table-column prop="xingMing" label="姓名" width="80"></el-table-column>
          <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
          <el-table-column prop="chuShengRQ" label="出生日期" width="120"></el-table-column>
          <el-table-column prop="chuangWeiHao" label="病区-床位号" width="100"></el-table-column>
          <el-table-column prop="ruYuanRQ" label="入院日期" width="200"></el-table-column>
          <el-table-column prop="bingQuRYRQ" label="病区入院日期" width="200"></el-table-column>
          <el-table-column prop="chuYuanRQ" label="出院日期" width="200"></el-table-column>
          <el-table-column prop="ruYuanZD" label="入院诊断" width="200"></el-table-column>
          <el-table-column prop="huaYanDan" label="化验单"></el-table-column>
          <el-table-column prop="fuYinZT" label="复印状态"></el-table-column>
          <el-table-column prop="bingLiZT" label="病历状态"></el-table-column>
          <el-table-column fixed="right" label="特殊病人维护" width="120">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">设置</el-button>
              <el-button type="text" size="small">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: '1',
          label: '消化内科'
        }
      ],
      value: '1',
      input: '',
      tableData: [
        {
          zhuanKe: '皮肤科',
          bingAnHao: '0014591109',
          xingMing: '张龙珠',
          xingBie: '女',
          chuShengRQ: '1962-11-12',
          chuangWeiHao: '056-003',
          ruYuanRQ: '2023-07-04 14:03:00',
          bingQuRYRQ: '2023-07-04 14:03:00',
          chuYuanRQ: '',
          ruYuanZD: '湿疹、大疱性类疱疮',
          huaYanDan: '化验单',
          fuYinZT: '未复印',
          bingLiZT: '未归档'
        }
      ],
      selectType: '姓名',
      selectContent: ''
    }
  },
  methods: {
    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 6px 10px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    padding: 9px 16px;
    color: #fff;
  }
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
