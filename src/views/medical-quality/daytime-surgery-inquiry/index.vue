<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <span>科室：</span>
        <el-select v-model="buMenDaiMa" @change="KeShiChange">
          <el-option
            v-for="item in buMenList"
            :key="item.daiMa"
            :label="item.mingCheng"
            :value="item.daiMa"
          ></el-option>
        </el-select>
      </div>
      <div class="header-item">
        <span>医生：</span>
        <el-select v-model="ysid">
          <el-option
            v-for="item in docterList"
            :key="item.yongHuID"
            :label="item.xingMing"
            :value="item.yongHuID"
          ></el-option>
        </el-select>
      </div>
      <div class="header-item">
        <span>条件：</span>
        <el-select v-model="tiaoJian">
          <el-option
            v-for="item in tiaoJianList"
            :key="item.id"
            :label="item.label"
            :value="item.label"
          ></el-option>
        </el-select>
      </div>
      <div class="header-item">
        <span>时间：</span>
        <el-date-picker
          v-model="shiJianFW"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </div>

      <el-button @click="queryRiJianSSList">查询</el-button>
      <el-button>导出到Excel</el-button>
      <el-button>直接导出到Excel</el-button>
    </div>
    <div class="content">
      <el-table max-height="648" :data="tableData">
        <el-table-column prop="zkid" label="科室">
          <template #default="{ row }">
            {{ getKeShiByZKID(row.zkid) }}
          </template>
        </el-table-column>
        <el-table-column prop="bqmc" label="病区"></el-table-column>
        <el-table-column prop="bingAnHao" width="90" label="病案号"></el-table-column>
        <el-table-column prop="brxm" label="病人姓名"></el-table-column>
        <el-table-column prop="zlz" width="100" label="治疗组"></el-table-column>
        <el-table-column prop="bqrysj" width="150" label="入院时间"></el-table-column>
        <el-table-column prop="bqcysj" width="150" label="出院时间"></el-table-column>
        <el-table-column prop="ryts" label="入院天数"></el-table-column>
        <el-table-column prop="lczd" width="160" label="诊断"></el-table-column>
        <el-table-column prop="zdysxm" label="主刀医师"></el-table-column>
        <el-table-column prop="zdysid" label="主刀ID"></el-table-column>
        <el-table-column prop="zyts" width="100" label="住院小时数"></el-table-column>
        <el-table-column prop="bassmc" width="150" label="病案首页主手术名称"></el-table-column>
        <el-table-column prop="bassicd" width="150" label="病案首页主手术编码"></el-table-column>
        <el-table-column prop="basslb" width="150" label="病案首页主手术类别"></el-table-column>
        <el-table-column prop="mzfs" label="麻醉方式"></el-table-column>
        <el-table-column prop="dyzsxm" label="一助姓名"></el-table-column>
        <el-table-column prop="dyzsid" label="一助ID"></el-table-column>
        <el-table-column prop="dezsxm" label="二助姓名"></el-table-column>
        <el-table-column prop="dezsid" label="二助ID"></el-table-column>
        <el-table-column prop="ssrq" width="150" label="实施手术时间"></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { getDaySurgeryList, getZhuanKeList } from '@/api/medical-quality'
import { getDoctorListByZKID } from '@/api/patient-info'
export default {
  data() {
    return {
      year: '',
      yearList: ['2023', '2024', '2025'],
      month: '',
      monthList: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
      buMenDaiMa: null,
      buMenList: [],
      ysid: null,
      docterList: [],
      tiaoJian: null,
      tiaoJianList: [
        { id: 0, label: '病区入院时间' },
        { id: 1, label: '病区出院时间' }
      ],
      shiJianFW: [],
      tableData: []
    }
  },
  computed: {
    getKeShiByZKID() {
      return function (ZKID) {
        return this.buMenList.find((item) => item.daiMa == ZKID)?.mingCheng
      }
    }
  },
  async mounted() {
    await this.getZhuanKeList()
    await getDaySurgeryList({
      bmid: '11',
      bqsj: '',
      istzd: null,
      jssj: '2025-01-01 00:00:00',
      kssj: '2010-01-01 00:00:00',
      ysid: ''
    })
  },
  methods: {
    async queryRiJianSSList() {
      let bqsj =
        this.tiaoJian == '病区入院时间' ? 'bqryrq' : this.tiaoJian == '病区出院时间' ? 'bqcyrq' : ''
      let res = await getDaySurgeryList({
        bmid: this.buMenDaiMa,
        bqsj: bqsj,
        istzd: null,
        jssj: this.shiJianFW[1],
        kssj: this.shiJianFW[0],
        ysid: this.ysid
      })
      this.tableData = res.data
    },
    async getZhuanKeList() {
      let res = await getZhuanKeList()
      if (res.hasError === 0) {
        this.buMenList = res.data
        this.buMenList.unshift({
          daiMa: '0',
          mingCheng: '全院'
        })
      }
    },
    async setDoctorListByZhuanKe(zhuanKeID) {
      if (zhuanKeID == 0) {
        this.docterList = [{ yongHuID: 0, xingMing: '全部' }]
        return
      }
      let res = await getDoctorListByZKID(zhuanKeID)
      if (res.hasError === 0) {
        this.docterList = res.data
        this.docterList.unshift({
          yongHuID: null,
          xingMing: '全部'
        })
      }
    },
    async KeShiChange(val) {
      this.ysid = null
      await this.setDoctorListByZhuanKe(val)
    }
  }
}
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}

::v-deep .year {
  .el-select {
    width: 92px;
  }
}
::v-deep .month {
  margin-left: 20px;
  .el-select {
    width: 92px;
  }
}

::v-deep .el-table--scrollable-y .el-table__body-wrapper::-webkit-scrollbar {
  // display: none;
}
</style>
