<template>
  <default-dialog
    :visible="visible"
    width="60%"
    title="筛查明细"
    @confirm="confirmClick"
    @cancel="updateVisible(false)"
    @open="initDialog()"
  >
    <span>
      <div class="dialog-component" style="max-height: 600px; overflow-y: auto">
        <el-button type="primary" @click="exportToExcel">导出到Excel</el-button>
        <el-table :data="dataList" style="width: 100%; margin-top: 10px" stripe border>
          <el-table-column
            v-for="(column, index) in columns"
            :key="index"
            :prop="column.value"
            :label="column.label"
            v-bind="column.props"
          ></el-table-column>
        </el-table>
      </div>
    </span>
  </default-dialog>
</template>

<script>
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import { getScmxByScidBlid } from '@/api/medical-quality'

export default {
  name: 'SelectDrawer',
  components: {
    DefaultDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    shaiChaID: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      dataList: [],
      columns: [
        { value: 'bingAnHao', label: '病案号', props: { fixed: true, width: '130' } },
        { value: 'bingRenXM', label: '姓名', props: { fixed: true, width: '160' } },
        { value: 'bingRenXB', label: '性别', props: { fixed: true, width: '160' } },
        { value: 'zhuanKeMC', label: '专科', props: { fixed: true, width: '160' } },
        { value: 'bingQuMC  ', label: '病区', props: { fixed: true, width: '180' } },
        { value: 'chuangWeiHao  ', label: '床位', props: { fixed: true, width: '180' } },
        { value: 'ruYuanRQ', label: '入院日期', props: { fixed: true } },
        { value: 'chuYuanRQ', label: '出院日期', props: { fixed: true } },
        { value: 'ruYuanZD', label: '入院诊断', props: { fixed: true } },
        { value: 'zhuanJiaYHID', label: '分配专家 ', props: { fixed: true } }
      ]
    }
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },

    handleSelectionChange(rows) {
      this.selectList = rows.map((row) => {
        return {
          buMenMC: row.mingCheng,
          buMenID: Number(row.daiMa)
        }
      })
    },
    async initDialog() {
      this.getData()
    },
    async getData() {
      const res = await getScmxByScidBlid({ shaiChaID: this.shaiChaID, bingLiID: 0 })
      if (res.hasError === 0) {
        console.log(res)
      }
    },
    exportToExcel() {
      if (this.dataList.length == 0) {
        return this.$message.error('暂无数据可导出')
      }
      const worksheet = XLSX.utils.json_to_sheet(
        this.dataList.map((data) => {
          let d = {}
          this.columns.forEach((col) => {
            if (col.value === 'bingRenXB') {
              d[col.label] = data[col.value] === '1' ? '男' : '女'
            } else {
              d[col.label] = data[col.value]
            }
          })
          return d
        })
      )
      // return
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      try {
        XLSX.writeFile(workbook, '筛查记录明细.xlsx')
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-component {
  padding: 0 10px;
  .search-head {
    button {
      --color-primary: #a66dd4;
    }
    :deep(.el-button--primary:hover),
    :deep(.el-button--primary:focus) {
      background: #ce8be0;
      border-color: #ce8be0;
    }
    display: flex;
    align-items: center;
    label {
      margin: 0 10px 0 10px;
    }
  }
}
</style>
