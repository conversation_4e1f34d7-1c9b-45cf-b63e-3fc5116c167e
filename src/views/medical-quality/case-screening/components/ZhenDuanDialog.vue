<template>
  <default-dialog
    :visible="visible"
    width="30%"
    title="诊断列表"
    @confirm="confirmClick"
    @cancel="updateVisible(false)"
    @open="initDialog()"
  >
    <span>
      <div class="dialog-component">
        <div class="search-head">
          <el-input
            ref="searchInput"
            v-model="searchValue"
            style="width: 80%"
            placeholder=""
          ></el-input>
          <el-button type="primary" @click="searchData()">查询</el-button>
        </div>
        <div style="margin-top: 10px">
          <el-select v-model="buWei1" style="width: 150px; margin-right: 10px">
            <el-option
              v-for="item in buWeiOptions"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
          <el-select v-model="buWei2" style="width: 150px; margin-right: 10px">
            <el-option
              v-for="item in buWeiOptions"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
        </div>
        <el-table
          :data="dataList"
          style="width: 100%; margin-top: 10px"
          stripe
          border
          highlight-current-row
          @current-change="
            (row) => {
              selectItem = row
              searchValue = row.zhenDuanMC
            }
          "
        >
          <el-table-column
            v-for="(column, index) in [
              { value: 'zhenDuanMC', label: '诊断名称', props: { fixed: true } },
              { value: 'icd', label: 'ICD编码', props: { fixed: true } }
            ]"
            :key="index"
            :prop="column.value"
            :label="column.label"
            v-bind="column.props"
          ></el-table-column>
        </el-table>
        <el-pagination
          background
          layout="total, prev, pager, next"
          :current-page.sync="currentPage"
          :page-size="10"
          :pager-count="5"
          :total="dataLength"
          @current-change="searchData()"
        ></el-pagination>
      </div>
    </span>
  </default-dialog>
</template>

<script>
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import { getZhenDuanByPageAndZhuanTai } from '@/api/medical-record-home'

export default {
  name: 'SelectDrawer',
  components: {
    DefaultDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dataList: [],
      dataLength: 0,
      searchValue: '',
      currentPage: 1,
      selectItem: null,
      benZhuanKe: true,
      buWei1: null,
      buWei2: null,
      buWeiOptions: [
        '?',
        '左',
        '右',
        '双',
        '上',
        '中',
        '下',
        '左上',
        '左下',
        '右上',
        '右下',
        '术后',
        '放疗后',
        '化疗后',
        '除外',
        '复查',
        '待查'
      ]
    }
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    async initDialog() {
      this.searchValue = ''
      this.currentPage = 1
      this.buWei1 = null
      this.buWei2 = null
      this.searchData()
    },
    async searchData() {
      try {
        const res = await getZhenDuanByPageAndZhuanTai({
          pageIndex: this.currentPage,
          pageSize: 10,
          wenBen: this.searchValue
        })
        if (res.hasError === 0) {
          this.dataList = res.data
          this.dataLength = res.extendData.total
        }
      } catch (error) {
        console.log(error)
      }
    },
    confirmClick() {
      this.updateVisible(false)
      this.$emit('confirm', `'(${this.buWei1})${this.selectItem.zhenDuanMC}(${this.buWei2})'`)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-component {
  padding: 0 10px;
  .search-head {
    button {
      --color-primary: #a66dd4;
    }
    :deep(.el-button--primary:hover),
    :deep(.el-button--primary:focus) {
      background: #ce8be0;
      border-color: #ce8be0;
    }
    display: flex;
    align-items: center;
    label {
      margin: 0 10px 0 10px;
    }
  }
}
</style>
