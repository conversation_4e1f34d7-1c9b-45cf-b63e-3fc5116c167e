<!-- 病例筛查 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="page-left">
        <div class="table-background">
          <div class="table-title">
            <div>
              <span class="bar" />
              历史筛查记录
            </div>
          </div>
          <div class="table-left table-component">
            <el-date-picker
              v-model="schDate"
              type="month"
              value-format="yyyy-MM-dd"
              style="width: 100%; margin-bottom: 5px"
              @change="geiScjlList"
            />
            <el-table :data="scjlList" style="width: 100%; flex-grow: 1" stripe border>
              <el-table-column
                v-for="(column, index) in [
                  { value: 'LiuShuiHao', label: '筛查流水号' },
                  { value: 'shaiChaBLZS', label: '内含份数', props: { width: '80' } }
                ]"
                :key="index"
                :prop="column.value"
                :label="column.label"
                v-bind="column.props"
              >
                <template v-if="column.label === '筛查流水号'" #default="{ row }">
                  <el-tooltip class="item" effect="light" placement="right">
                    <div slot="content">
                      <span>筛查条件:</span>
                      {{ row.shaiChaTJ }}
                      <br />
                      <span>筛查人员:</span>
                      {{ row.shaiChaRYYHXM }}
                      <br />
                      <span>筛查时间:</span>
                      {{ row.shaiChaSJ }}
                      <br />
                    </div>
                    <el-button type="text" @click="openScjl(row)">
                      {{ row.shaiChaSJ.replace(/[\s|:|-]*/g, '') }}
                    </el-button>
                  </el-tooltip>
                </template>
                <template v-else-if="column.label === '内含份数'" #default="{ row }">
                  <div style="display: flex; align-items: center; justify-content: space-evenly">
                    {{ row.shaiChaBLZS }}
                    <el-button
                      type="text"
                      icon="el-icon-delete"
                      @click="deleteScjl(row)"
                    ></el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div style="display: flex; flex-direction: column; flex-grow: 1; width: 100%">
        <div class="head-search">
          <el-tabs v-model="searchType" type="border-card">
            <el-tab-pane label="按条件查" name="1" style="min-height: 110px">
              <div class="head-row">
                <el-select v-model="scType" style="width: 120px; margin-right: 10px">
                  <el-option
                    v-for="item in [
                      { label: '按专科', value: '1' },
                      { label: '按病区', value: '2' }
                    ]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <span class="search-label">筛选专科:</span>
                <el-select
                  v-model="zhuanKeID"
                  style="width: 150px; margin-right: 10px"
                  @change="handleChange"
                >
                  <el-option
                    v-for="item in zhuanKeOptions"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
                <el-button icon="el-icon-plus" @click="openSelect(scType)">
                  点击选择{{ scType === '1' ? '专科(住院药品医嘱普通筛查用/多选)' : '病区' }}
                </el-button>
                <span class="search-label">筛查医嘱类型:</span>
                <el-select v-model="yiZhuLX" style="width: 150px; margin-right: 10px">
                  <el-option
                    v-for="item in [
                      { label: '药品医嘱', value: '1' },
                      { label: '食品医嘱', value: '2' }
                    ]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <span class="search-label">筛查方式:</span>
                <el-select
                  v-model="scMethod"
                  style="width: 120px; margin-right: 10px"
                  @change="handleChange"
                >
                  <el-option
                    v-for="item in [
                      { label: '按病区', value: '1' },
                      { label: '按治疗组', value: '2' }
                    ]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <span class="search-label">{{ scMethod === '1' ? '病区' : ' 治疗组' }}:</span>
                <el-select
                  v-if="scMethod === '1'"
                  v-model="scBuMen"
                  style="width: 120px; margin-right: 10px"
                >
                  <el-option
                    v-for="item in bingQuOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <el-select
                  v-else-if="scMethod === '2'"
                  v-model="scBuMen"
                  style="width: 120px; margin-right: 10px"
                >
                  <el-option
                    v-for="item in zhiLiaoZuOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <span class="search-label">筛查总数:</span>
                <el-input-number v-model="scSum" style="width: 120px; margin-right: 10px" />
              </div>

              <div class="head-row">
                <span class="search-label">药品过滤:</span>
                <el-button type="text">设置</el-button>

                <span class="search-label">且医生过滤:</span>

                <el-input
                  :value="selectYiSheng?.xingMing"
                  style="width: 150px; margin-right: 10px"
                  @focus="yiShengVisible = true"
                />
                <el-button type="text" @click="zhenDuanVisible = true">
                  诊断过滤(点击这里)
                </el-button>
                <el-input
                  :value="zhenDuanList.join(',')"
                  style="width: 600px; margin-right: 10px"
                />
                <el-button type="text" @click="zhenDuanList = []">清空诊断</el-button>
              </div>
              <div>
                <span class="search-label">附加条件:</span>
                <el-select
                  v-model="fuJiaTJ"
                  style="width: 120px; margin-right: 10px"
                  @change="
                    (v) => {
                      zaiYuanBR = v === '1'
                    }
                  "
                >
                  <el-option
                    v-for="item in [
                      { label: '在院病人', value: '1' },
                      { label: '入院时间', value: '2' },
                      { label: '出院时间', value: '3' }
                    ]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <template v-if="fuJiaTJ !== '1'">
                  <el-date-picker
                    v-model="searchDate"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd"
                    style="margin-right: 10px"
                  />
                  (*范围三个月)
                </template>
                <template v-if="fuJiaTJ === '2'">
                  <el-checkbox v-model="zaiYuanBR">在院病人</el-checkbox>
                </template>
                <el-button type="primary" icon="el-icon-search" @click="getdataList">
                  筛查
                </el-button>
                <el-button type="primary" icon="el-icon-search" @click="() => {}">
                  规培医生医嘱筛查(附加条件必选)
                </el-button>
                <el-button type="primary" icon="el-icon-search" @click="() => {}">
                  临床路径内临时医嘱筛查
                </el-button>
                <el-button icon="el-icon-plus" @click="() => {}">分配专家</el-button>
              </div>
            </el-tab-pane>
            <el-tab-pane label="按病案号查" name="2" style="min-height: 110px">
              <div style="display: flex">
                <div>
                  <span class="search-label">病案号:</span>
                  <el-input v-model="bingAnHao" style="width: 200px; margin-right: 10px" />
                  <el-button type="primary" icon="el-icon-search" @click="getBingAnHaoList()">
                    查询
                  </el-button>
                  <el-button type="primary" icon="el-icon-search" @click="getOnedata()">
                    筛查
                  </el-button>
                </div>
                <div>
                  <el-checkbox-group
                    v-model="selectBingLi"
                    style="display: flex; flex-direction: column"
                  >
                    <el-checkbox
                      v-for="item in bingLiList"
                      :key="item.bingLiID"
                      :label="item.bingLiID"
                    >
                      <div style="display: flex">
                        <div style="width: 180px">{{ item.xingMing }}({{ item.keShiMC }})</div>
                        <div style="width: 250px">入院日期:{{ item.ruYuanRQ ?? '无' }}</div>
                        <div style="width: 180px">出院日期: {{ item.chuYuanRQ ?? '无' }}</div>
                      </div>
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="table-background">
          <div class="table-title">
            <div>
              <span class="bar" />
              病人列表
            </div>
          </div>
          <div class="table-component">
            <el-table :data="currentList" style="width: 100%; flex-grow: 1" stripe border>
              <el-table-column
                v-for="(column, index) in columns"
                :key="index"
                :prop="column.value"
                :label="column.label"
                v-bind="column.props"
              >
                <template v-if="column.label === '性别'" #default="{ row }">
                  {{ row.bingRenXB === '1' ? '男' : '女' }}
                </template>
                <template v-else #default="{ row }">
                  {{ row[column.value] ?? row.bingRenXX?.[column.value] }}
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="total, prev, pager, next"
              :current-page.sync="currentPage"
              :page-size="8"
              :pager-count="5"
              :total="dataList.length"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <zhuan-ke-dialog
      :visible.sync="zhuanKeVisible"
      @confirm="
        (zhuankeList) => {
          selectZhuankeList = zhuankeList
        }
      "
    />
    <bing-qu-dialog
      :visible.sync="bingQuVisible"
      @confirm="
        (bingQuList) => {
          selectBingQuList = bingQuList
        }
      "
    />
    <zhen-duan-dialog
      :visible.sync="zhenDuanVisible"
      @confirm="
        (zhenDuan) => {
          zhenDuanList.push(zhenDuan)
        }
      "
    />

    <doctor-selector
      class="doctor-selector"
      :request="request"
      :visible.sync="yiShengVisible"
      @handleChangeVisible="
        (v) => {
          yiShengVisible = v
        }
      "
      @handleConfim="
        (row) => {
          selectYiSheng = row
        }
      "
    />

    <shai-cha-details :shai-cha-i-d="selectShaiChaID" :visible.sync="shaiChaVisible" />
  </div>
</template>

<script>
import {
  getZhuanKeList,
  getBrList,
  getTherapyGroupsByZhuanKeID,
  getScjlListByTimeType,
  deleteShaiChaJL,
  getBrSingle
} from '@/api/medical-quality'
import { getBingqQuListByZhuanKeID } from '@/api/patient'
import { getBinRenXXByParam } from '@/api/information'
import { mapState } from 'vuex'
import request from '@/utils/request'
import ZhuanKeDialog from './components/ZhuanKeDialog.vue'
import BingQuDialog from './components/BingQuDialog.vue'
import ZhenDuanDialog from './components/ZhenDuanDialog.vue'
import DoctorSelector from '@/components/PersonSelector/DoctorSelector.vue'
import ShaiChaDetails from './components/ShaiChaDetails.vue'

export default {
  name: 'CaseScreening',
  components: {
    ZhuanKeDialog,
    BingQuDialog,
    ZhenDuanDialog,
    DoctorSelector,
    ShaiChaDetails
  },
  data() {
    return {
      request,
      zhuanKeVisible: false,
      bingQuVisible: false,
      zhenDuanVisible: false,
      yiShengVisible: false,
      shaiChaVisible: false,

      selectZhuankeList: [],
      selectBingQuList: [],
      selectYiSheng: null,
      selectShaiChaID: null,

      searchType: '1',
      scType: '1', //筛查类型 1专科 或 2病区
      scMethod: '1', //筛查方式
      scSum: 0, //筛查总数
      scBuMen: null,
      schDate: null,
      bingAnHao: '',
      currentPage: 1,
      rate: '',
      zhuanKeID: '0',
      yiZhuLX: '1', //医嘱类型
      fuJiaTJ: '1', //附加条件
      zaiYuanBR: false,
      zhuanKeOptions: [],
      bingQuOptions: [],
      zhiLiaoZuOptions: [],
      searchDate: [],
      zhenDuanList: [], //筛查记录列表诊断列表
      bingLiList: [], //按病案号查病历列表
      selectBingLi: [], //选择的病历
      scjlList: [], //筛查记录列表
      dataList: [],
      columns: [
        { value: 'bingAnHao', label: '病案号', props: { fixed: true, width: '130' } },
        { value: 'bingRenXM', label: '姓名', props: { fixed: true, width: '160' } },
        { value: 'bingRenXB', label: '性别', props: { fixed: true, width: '160' } },
        { value: 'zhuanKeMC', label: '专科', props: { fixed: true, width: '160' } },
        { value: 'bingQuMC  ', label: '病区', props: { fixed: true, width: '180' } },
        { value: 'ruYuanRQ', label: '入院日期', props: { fixed: true } },
        { value: 'chuYuanRQ', label: '出院日期', props: { fixed: true } },
        { value: 'ruYuanZD', label: '入院诊断', props: { fixed: true } }
      ]
    }
  },
  computed: {
    ...mapState({
      patientInfo: ({ patient }) => patient.initInfo
    }),
    currentList() {
      const start = (this.currentPage - 1) * 8
      return this.dataList.slice(start, start + 8)
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    async handleChange() {
      if (this.scType === '1') {
        if (this.zhuanKeID === '0') {
          this.bingQuZhiLiaoZuOptions = []
          this.scBuMen = null
        } else {
          if (this.scMethod === '1') {
            const res = await getBingqQuListByZhuanKeID({ ZKID: this.zhuanKeID })
            if (res.hasError === 0) {
              this.bingQuOptions = [
                {
                  value: '0',
                  label: '所有病区'
                },
                ...res.data.map((item) => {
                  return { value: item.buMenID, label: item.buMenMC }
                })
              ]
            }
          } else if (this.scMethod === '2') {
            const res = await getTherapyGroupsByZhuanKeID({ zhuanKeID: this.zhuanKeID })
            if (res.hasError === 0) {
              this.zhiLiaoZuOptions = [
                {
                  value: '0',
                  label: '所有治疗组'
                },
                ...res.data.map((item) => {
                  return { value: item.id, label: item.mingCheng }
                })
              ]
            }
          }

          this.scBuMen = '0'
        }
      }
    },
    openSelect(type) {
      if (type === '1') {
        this.zhuanKeVisible = true
      } else if (type === '2') {
        this.bingQuVisible = true
      }
    },
    async geizhuanKeOptions() {
      const res = await getZhuanKeList()
      if (res.hasError === 0) {
        this.zhuanKeOptions = [
          {
            daiMa: '0',
            mingCheng: '所有专科'
          },
          ...res.data
        ]
      }
    },
    //获取筛查记录
    async geiScjlList() {
      const startDate = new Date(new Date(this.schDate).setDate(1))
      const endDate = new Date(new Date(startDate).setMonth(startDate.getMonth() + 1))
      const res = await getScjlListByTimeType({
        endDate: this.dateFormat(endDate) + ' 00:00:00',
        startDate: this.dateFormat(startDate) + ' 00:00:00',
        shaiChaLX: '1'
      })
      if (res.hasError === 0) {
        this.scjlList = res.data.sort((a, b) => {
          return a.shaiChaSJ > b.shaiChaSJ ? -1 : 1
        })
      }
    },
    async getdataList() {
      if (this.scSum === 0) {
        this.$message.error('请输入筛查数量...')
        return
      }
      const zhuanKeMC = this.zhuanKeOptions.find((item) => {
        return item.daiMa === this.zhuanKeID
      })?.mingCheng
      const buMenMC =
        this.scMethod === '1'
          ? this.bingQuOptions.find((item) => {
              return item.value === this.scBuMen
            })?.label
          : this.zhiLiaoZuOptions.find((item) => {
              return item.value === this.scBuMen
            })?.label
      const scMethodMC = this.scMethod === '1' ? '病区' : '治疗组'
      const fuJiaTJMC = {
        1: '在院病人',
        2: '入院时间',
        3: '出院时间'
      }[this.fuJiaTJ]
      let shaiChaTJ = `筛查专科:${this.zhuanKeID}_${zhuanKeMC}，${scMethodMC}:${this.scBuMen}_${buMenMC}，筛查总数:${this.scSum}，药品过滤:文本:，附加条件:${fuJiaTJMC}`

      const res = await getBrList({
        bingQuID: 0,

        endTime: this.searchDate[1] + ' 00:00:00', //结束时间
        fuJiaTJ: this.fuJiaTJ, //附加条件
        ls_wykc: '', //wykc
        shaiChaLX: this.scType, //筛查类型
        shaiChaTJ, //筛查条件
        shaiChaZS: this.scSum, //筛查总数
        shiFouZY: this.zaiYuanBR, //是否住院
        startTime: this.searchDate[0] + ' 00:00:00', //结束时间
        waiGouYaoMC: '', //外购药品名称
        yaoPinIds: [0], //药品id列表
        yaoPinSL: 0, //药品数量
        yiShengID: this.selectYiSheng?.yongHuID || 0, //医生id
        zhenDuanList: this.zhenDuanList, //诊断列表
        zhiLiaoZuID: 0, //治疗组id
        zhuanKeID: this.zhuanKeID //专科id
      })
      if (res.hasError === 0) {
        this.dataList = res.data
        this.$message.success('筛查成功')
        this.geiScjlList()
      }
    },
    async getOnedata() {
      if (!Array.isArray(this.selectBingLi) || this.selectBingLi.length !== 1) {
        this.$message.error('请选择一条记录...')
        return
      }
      const res = await getBrSingle({
        bingLiID: this.selectBingLi[0],
        shaiChaLX: '1',
        shaiChaTJ: '指定病人'
      })
      if (res.hasError === 0) {
        let bingRenXX = this.bingLiList.find((item) => {
          return item.bingLiID === this.selectBingLi[0]
        })

        this.dataList = [{ ...res.data, bingRenXX }]
        this.$message.success('筛查成功')
        this.geiScjlList()
      }
    },
    async getBingAnHaoList() {
      const res = await getBinRenXXByParam({
        type: 1,
        param: this.bingAnHao,
        yongHuID: this.patientInfo.yiShiYHID
      })
      if (res.hasError === 0) {
        this.bingLiList = res.data
        this.$message.success('查询成功')
      }
    },
    async openScjl(row) {
      this.selectShaiChaID = row.shaiChaID
      this.shaiChaVisible = true
    },
    deleteScjl(row) {
      this.$confirm('确定要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await deleteShaiChaJL({ shaiChaID: row.shaiChaID })
          if (res.hasError === 0) {
            this.$message.success('删除成功')
            this.geiScjlList()
          }
        })
        .catch(() => {
          // 取消删除，不做任何操作
        })
    },
    initPage() {
      let date = new Date()
      this.searchDate = [this.dateFormat(new Date(date.setDate(1))), this.dateFormat()]
      this.schDate = this.dateFormat()
      this.geizhuanKeOptions()
      this.geiScjlList()
    },
    //时间转换标准格式
    dateFormat(date = new Date()) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day)
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    padding: 10px;
    height: 100%;
    .page-left {
      margin-right: 5px;
      display: flex;
    }
    .head-search {
      margin-bottom: 10px;
      .head-row {
        margin-bottom: 5px;
      }
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
        margin-right: 10px;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        .bar {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        margin-top: 10px;
      }

      .table-left {
        width: 250px;
      }
    }
  }
}
</style>
