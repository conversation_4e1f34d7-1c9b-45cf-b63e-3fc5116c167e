<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <span>部门：</span>
        <el-select v-model="buMenDaiMa">
          <el-option
            v-for="item in buMenList"
            :key="item.daiMa"
            :label="item.mingCheng"
            :value="item.daiMa"
          ></el-option>
        </el-select>
      </div>
      <div class="header-item">
        <span>出院日期：</span>
        <div class="year">
          <span>年份：</span>
          <el-select v-model="year">
            <el-option v-for="item in yearList" :key="item" :label="item" :value="item"></el-option>
          </el-select>
          <span>年</span>
        </div>
        <div class="month">
          <span>月份：</span>
          <el-select v-model="month">
            <el-option
              v-for="item in monthList"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
          <span>月</span>
        </div>
      </div>
      <el-button @click="queryShouShuTZDList">查询</el-button>
    </div>
    <div class="content">
      <el-table max-height="648" :data="tableData">
        <el-table-column type="index"></el-table-column>
        <el-table-column prop="bmmc" label="部门"></el-table-column>
        <el-table-column prop="brxm" label="患者姓名"></el-table-column>
        <el-table-column label="性别">
          <template #default="{ row }">
            {{ row.brxb === '1' ? '男' : '女' }}
          </template>
        </el-table-column>
        <el-table-column prop="csrq" label="出生日期"></el-table-column>
        <el-table-column prop="blid" label="病案号"></el-table-column>
        <el-table-column prop="blid" label="BLID"></el-table-column>
        <el-table-column prop="zlzz" label="治疗组长"></el-table-column>
        <el-table-column prop="ssmc" label="手术名称"></el-table-column>
        <el-table-column prop="zd" label="诊断"></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { getSurgeryNoticeList, getZhuanKeList } from '@/api/medical-quality'
export default {
  data() {
    return {
      year: '',
      yearList: ['2023', '2024', '2025'],
      month: '',
      monthList: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
      buMenDaiMa: null,
      buMenList: [],
      tableData: []
    }
  },
  async mounted() {
    await this.getZhuanKeList()
  },
  methods: {
    async queryShouShuTZDList() {
      let res = await getSurgeryNoticeList({
        departmentCode: this.buMenDaiMa,
        month: this.month,
        year: this.year
      })
      this.tableData = res.data
    },
    async getZhuanKeList() {
      let res = await getZhuanKeList()
      if (res.hasError === 0) {
        this.buMenList = res.data
        this.buMenList.unshift({
          daiMa: '0',
          mingCheng: '全部'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}

::v-deep .year {
  .el-select {
    width: 92px;
  }
}
::v-deep .month {
  margin-left: 20px;
  .el-select {
    width: 92px;
  }
}

::v-deep .el-table--scrollable-y .el-table__body-wrapper::-webkit-scrollbar {
  // display: none;
}
</style>
