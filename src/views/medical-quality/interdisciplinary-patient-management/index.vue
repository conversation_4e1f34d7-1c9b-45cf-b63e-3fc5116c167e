<template>
  <div class="container">
    <!-- 医务处指定跨科病人 -->
    <div class="filter-container">
      <el-button type="primary" size="mini" @click="handleAdd">新增</el-button>
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd HH:mm:ss"
      ></el-date-picker>
      <el-checkbox v-model="queryParams.yuWoYG" label="与我有关"></el-checkbox>
      <el-button
        type="primary"
        size="mini"
        icon="el-icon-search"
        class="purple-button"
        @click="handleQuery"
      >
        查询
      </el-button>
    </div>
    <div class="table-container">
      <div class="title">医务处指定跨科病人</div>
      <el-table
        v-loading="loading"
        :data="pagedTableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 80px)"
        row-key="id"
        size="mini"
      >
        <el-table-column
          prop="bingRenXM"
          label="病人姓名"
          width="100"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="bingAnHao"
          label="病案号"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="zhuanKeMC"
          label="来源专科"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="kuaKeZKMC"
          label="跨科专科"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="kuaKeYSMC"
          label="指定医生"
          width="100"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="chuZhiYSMC"
          label="录入人员"
          width="100"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="shouCiLRSJ"
          label="首次录入时间"
          width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column prop="zhuangTaiBZ" label="状态" width="100" align="center">
          <template #default="{ row }">
            {{ formatZhuangTai(row.zhuangTaiBZ) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="{ row }">
            <el-button
              v-if="row.zhuangTaiBZ === '1'"
              type="warning"
              size="mini"
              @click="handleStop(row)"
            >
              停止
            </el-button>
            <el-button v-else type="success" size="mini" @click="handleStart(row)">开始</el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="xiuGaiSJ"
          label="最后修改时间"
          width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="jieShuSJ"
          label="权限结束时间"
          width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="beiZhu"
          label="备注"
          min-width="150"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page.sync="currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size.sync="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <!-- 新增记录弹窗 -->
    <add-record-dialog :visible.sync="addDialogVisible" @success="handleQuery"></add-record-dialog>
  </div>
</template>

<script>
import { getListByCondition2, startKkzljl, stopKkzljl } from '@/api/medical-quality'
import { format, addDays, subDays } from 'date-fns'
import AddRecordDialog from './components/AddRecordDialog.vue'

export default {
  name: 'InterdisciplinaryPatientManagement',
  components: {
    AddRecordDialog
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        kaiShiSJ: '',
        jieShuSJ: '',
        yuWoYG: false
      },
      dateRange: [],
      // 表格数据
      tableData: [],
      // 分页参数
      currentPage: 1,
      pageSize: 50,
      total: 0,
      // 弹窗控制
      addDialogVisible: false,
      // 加载状态
      loading: false
    }
  },
  computed: {
    // 分页后的表格数据
    pagedTableData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.tableData.slice(start, end)
    }
  },
  created() {
    this.init()
  },
  methods: {
    // 初始化
    init() {
      // 设置默认时间范围为15天前到明天
      const end = addDays(new Date(), 1)
      const start = subDays(new Date(), 15)
      this.dateRange = [format(start, 'yyyy-MM-dd 00:00:00'), format(end, 'yyyy-MM-dd 00:00:00')]

      // 初始查询
      this.handleQuery()
    },
    // 格式化状态
    formatZhuangTai(zhuangTaiBZ) {
      switch (zhuangTaiBZ) {
        case '1':
          return '正常'
        case '0':
          return '停用'
        case '2':
          return '过期'
        default:
          return ''
      }
    },
    // 查询数据
    async handleQuery() {
      // 设置查询参数
      const params = { ...this.queryParams }
      if (this.dateRange && this.dateRange.length === 2) {
        params.kaiShiSJ = this.dateRange[0]
        params.jieShuSJ = this.dateRange[1]
      }
      params.yuWoYG = params.yuWoYG ? '1' : '0'
      try {
        this.loading = true
        const res = await getListByCondition2(params)
        this.loading = false

        if (res.hasError === 0 && res.data) {
          this.tableData = res.data
          this.total = res.data.length
          // 重置分页
          this.currentPage = 1
        }
      } catch (error) {
        this.loading = false
        console.error('查询跨科病人列表失败', error)
      }
    },
    // 新增记录
    handleAdd() {
      this.addDialogVisible = true
    },
    // 启用记录
    async handleStart(row) {
      try {
        await this.$confirm('确认要启用该跨科治疗记录吗?', '提示', {
          type: 'warning'
        })

        this.loading = true
        const res = await startKkzljl({ ID: row.id })
        this.loading = false

        if (res.hasError === 0) {
          this.$message.success('启用成功')
          await this.handleQuery()
        } else {
          this.$message.error(res.errorMessage || '启用失败')
        }
      } catch (error) {
        this.loading = false
        if (error !== 'cancel') {
          console.error('启用跨科治疗记录失败', error)
          this.$message.error('启用跨科治疗记录失败')
        }
      }
    },
    // 停用记录
    async handleStop(row) {
      try {
        await this.$confirm('确认要停用该跨科治疗记录吗?', '提示', {
          type: 'warning'
        })

        this.loading = true
        const res = await stopKkzljl({ ID: row.id })
        this.loading = false

        if (res.hasError === 0) {
          this.$message.success('停用成功')
          await this.handleQuery()
        } else {
          this.$message.error(res.errorMessage || '停用失败')
        }
      } catch (error) {
        this.loading = false
        if (error !== 'cancel') {
          console.error('停用跨科治疗记录失败', error)
          this.$message.error('停用跨科治疗记录失败')
        }
      }
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.pageSize = val
    },
    // 当前页变化
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  padding: 10px;
  background: #fff;
}
.filter-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  display: flex;
  gap: 10px;
  align-items: center;

  .purple-button {
    background: #a66dd4;
    border: 1px solid #a66dd4;
    &:hover,
    &:focus {
      background: #ce8be0;
      border-color: #ce8be0;
    }
  }
}

.table-container {
  height: calc(100% - 62px);
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
  }

  .pagination-container {
    height: 58px;
    padding: 10px;
    background-color: #eaf0f9;
    text-align: right;
  }
}
</style>
