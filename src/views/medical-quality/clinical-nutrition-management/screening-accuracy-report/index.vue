<template>
  <!-- 营养风险筛查点评正确率报表 -->
  <div class="container">
    <div class="filter-container">
      <div class="filter-col">
        <div class="filter-item">
          <span class="filter-label">时间范围：</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </div>
      </div>
      <div class="filter-col">
        <div class="filter-item">
          <el-button type="primary" size="mini" class="purple-button" @click="handleQuery">
            查询
          </el-button>
          <el-button type="success" size="mini" @click="exportToExcel">导出Excel</el-button>
        </div>
      </div>
    </div>

    <!-- 数据展示区域 -->
    <div class="table-container">
      <div class="title">
        <span>营养风险筛查点评正确率报表</span>
        <span class="total-count">共{{ tableData.length }}条数据</span>
      </div>
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 30px)"
        row-key="buMenID"
        size="mini"
      >
        <el-table-column prop="buMenMC" label="病区"></el-table-column>
        <el-table-column prop="yingYangSTotal" label="营养师点评数"></el-table-column>
        <el-table-column prop="yingYangSRight" label="营养师点评合理数"></el-table-column>
        <el-table-column prop="yingYangSWrong" label="营养师点评不合理数"></el-table-column>
        <el-table-column label="营养师点评正确率(%)" width="150">
          <template #default="{ row }">
            {{ row.yingYangSRightRate ? `${row.yingYangSRightRate}%` : '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="huShiZTotal" label="护士长点评数"></el-table-column>
        <el-table-column prop="huShiZRight" label="护士长点评合理数"></el-table-column>
        <el-table-column prop="huShiZWrong" label="护士长点评不合理数"></el-table-column>
        <el-table-column label="护士长点评正确率(%)" width="150">
          <template #default="{ row }">
            {{ row.huShiZRightRate ? `${row.huShiZRightRate}%` : '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="jiaoChaDPTotal" label="交叉点评点评数"></el-table-column>
        <el-table-column prop="jiaoChaDPRight" label="交叉点评合理数"></el-table-column>
        <el-table-column prop="jiaoChaDPWrong" label="交叉点评不合理数"></el-table-column>
        <el-table-column label="交叉点评正确率(%)" width="150">
          <template #default="{ row }">
            {{ row.jiaoChaDPRightRate ? `${row.jiaoChaDPRightRate}%` : '--' }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getCommentStatisticInAllWards } from '@/api/medical-quality'
import { format, startOfMonth } from 'date-fns'
import * as XLSX from 'xlsx'

export default {
  name: 'ScreeningAccuracyReport',
  data() {
    return {
      // 查询参数
      queryParams: {
        kaiShiSJ: '',
        jieShuSJ: ''
      },
      dateRange: [],
      // 表格数据
      tableData: [],
      // 加载状态
      loading: false
    }
  },
  created() {
    this.init()
  },
  methods: {
    // 初始化
    async init() {
      // 设置默认时间范围：当月1号到当天
      const start = startOfMonth(new Date())
      const end = new Date()
      this.dateRange = [format(start, 'yyyy-MM-dd 00:00:00'), format(end, 'yyyy-MM-dd 23:59:59')]

      // 初始查询
      await this.handleQuery()
    },

    // 查询数据
    async handleQuery() {
      // 设置查询参数
      const params = { ...this.queryParams }
      if (this.dateRange && this.dateRange.length === 2) {
        params.kaiShiSJ = this.dateRange[0]
        params.jieShuSJ = this.dateRange[1]
      }

      try {
        this.loading = true
        const res = await getCommentStatisticInAllWards(params)
        this.loading = false

        if (res.hasError === 0 && res.data) {
          this.tableData = res.data || []
        }
      } catch (error) {
        this.loading = false
        console.error('查询数据失败:', error)
        this.$message.error(error.errorMessage || '查询数据失败')
        this.tableData = []
      }
    },

    // 导出Excel
    exportToExcel() {
      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      try {
        // 准备导出数据
        const exportData = this.tableData.map((item) => {
          return {
            病区: item.buMenMC,
            营养师点评数: item.yingYangSTotal || 0,
            营养师点评合理数: item.yingYangSRight || 0,
            营养师点评不合理数: item.yingYangSWrong || 0,
            '营养师点评正确率（%）': item.yingYangSRightRate ? `${item.yingYangSRightRate}%` : '--',
            护士长点评数: item.huShiZTotal || 0,
            护士长点评合理数: item.huShiZRight || 0,
            护士长点评不合理数: item.huShiZWrong || 0,
            '护士长点评正确率（%）': item.huShiZRightRate ? `${item.huShiZRightRate}%` : '--',
            交叉点评点评数: item.jiaoChaDPTotal || 0,
            交叉点评点评合理数: item.jiaoChaDPRight || 0,
            交叉点评点评不合理数: item.jiaoChaDPWrong || 0,
            '交叉点评点评正确率（%）': item.jiaoChaDPRightRate
              ? `${item.jiaoChaDPRightRate}%`
              : '--'
          }
        })

        // 创建工作表
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

        // 生成文件名
        const fileName = `营养风险筛查点评正确率报表_${format(new Date(), 'yyyy-MM-dd')}.xlsx`

        // 下载文件
        XLSX.writeFile(workbook, fileName)
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  padding: 10px;
  background: #fff;
}

.filter-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  display: flex;
}

.filter-col {
  display: flex;
  padding: 0 8px;

  .filter-item {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.filter-label {
  font-weight: bold;
  min-width: 80px;
}

.purple-button {
  background: #a66dd4;
  border: 1px solid #a66dd4;
  &:hover,
  &:focus {
    background: #ce8be0;
    border-color: #ce8be0;
  }
}

.table-container {
  height: calc(100% - 62px);
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .total-count {
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
