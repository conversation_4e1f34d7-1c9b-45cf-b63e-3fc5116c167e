<template>
  <div class="container">
    <div class="content-left">
      <div class="content-header">
        <div class="title">康复治疗执行记录</div>
      </div>
      <div class="table">
        <el-table
          style="width: 650px"
          border
          stripe
          highlight-current-row
          size="mini"
          :data="tableData"
          @current-change="handleCurrentChange"
        >
          <el-table-column prop="mb" width="60" label="">
            <template slot-scope="scope">
              <p v-if="scope.row.mb === '目标'" style="color: #5d88d1">目标</p>
              <p v-else style="color: #ff8c40">{{ scope.row.mb }}</p>
            </template>
          </el-table-column>
          <el-table-column prop="shengChengSJ" width="160" label="时间"></el-table-column>
          <el-table-column prop="yiZhuMC" label="名称"></el-table-column>
          <el-table-column prop="yongHuXM" width="80" label="医师姓名"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" width="80" label="完成情况" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.zhuangTaiBZ === '已完成'">已完成</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="content-right">
      <div v-if="btName !== ''" class="right-top">
        <div class="table">
          <el-table style="width: 502px" border stripe size="mini" :data="tableData2">
            <el-table-column prop="pingGuXMMC" width="300" label="评估项目名称"></el-table-column>
            <el-table-column prop="pingGuJG" width="100" label="评估结果"></el-table-column>
            <el-table-column prop="pingGuFL" width="100" label="评估分类" align="center">
              <el-button type="text">详情</el-button>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div v-if="btName !== ''" class="right-bottom">
        <div class="content-header">
          <div class="title">{{ btName }}</div>
        </div>
        <div class="table">
          <el-table style="width: 502px" border stripe size="mini" :data="tableData3">
            <el-table-column prop="xiangMuMC" width="200" label="项目名称"></el-table-column>
            <el-table-column prop="pinLv" width="60" label="频率"></el-table-column>
            <el-table-column prop="buWeiOrLF" label="部位"></el-table-column>
            <el-table-column prop="zhiXingRY" width="100" label="执行人员" align="center">
              <template slot-scope="scope">
                <el-image :src="scope.row.zhiXingRY">
                  <!--                  <div slot="error" class="image-slot">-->
                  <!--                    <i class="el-icon-picture-outline"></i>-->
                  <!--                  </div>-->
                </el-image>
              </template>
            </el-table-column>
            <el-table-column prop="shiChang" width="60" label="时长"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getKangFuZLJL, getKangFuZLYZ, getUnFinishedExamDataAll } from '@/api/inpatient-order'

export default {
  name: 'RehabilitationExecutionRecord',
  data() {
    return {
      tableData: [],
      tableData2: [],
      tableData3: [],
      btName: ''
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      const res = await getKangFuZLJL({
        bingLiID: this.bingLiID
      })

      let data_list = []
      for (const d of res.data.zhiXingJL) {
        const data = {
          shengChengSJ: d.shengChengSJ,
          yiZhuMC: d.yiZhuMC,
          yongHuXM: d.yongHuXM,
          zhuangTaiBZ: d.zhuangTaiBZ,
          zhiLiaoXMID: d.zhiLiaoXMID
        }
        data_list.push(data)
      }
      let PfjlList = []
      for (const d of res.data.pingFenJL) {
        const data = {
          shengChengSJ: d.pingFenSJ,
          yiZhuMC: d.zhongWenMing,
          yongHuXM: d.yongHuXM,
          zhuangTaiBZ: '未完成'
        }
        data_list.push(data)
        PfjlList.push(data)
        PfjlList.sort((a, b) => a.shengChengSJ.localeCompare(b.shengChengSJ))
        for (let i = 0; i < PfjlList.length; i++) {
          if (i === 0) {
            PfjlList[i]['mb'] = '目标'
          } else {
            PfjlList[i]['mb'] = '效果' + i
          }
        }
      }
      let PgjlList = []
      for (const d of res.data.pingGuJL) {
        const data = {
          shengChengSJ: d.jiLuSJ,
          yiZhuMC: d.mingCheng,
          yongHuXM: d.yongHuXM,
          zhuangTaiBZ: '未完成'
        }
        data_list.push(data)
        PgjlList.push(data)
        PgjlList.sort((a, b) => a.shengChengSJ.localeCompare(b.shengChengSJ))
        for (let i = 0; i < PgjlList.length; i++) {
          if (i === 0) {
            PgjlList[i]['mb'] = '目标'
          } else {
            PgjlList[i]['mb'] = '效果' + i
          }
        }
      }
      data_list.sort((a, b) => a.shengChengSJ.localeCompare(b.shengChengSJ))
      console.log(data_list)
      this.tableData = data_list
    },
    async handleCurrentChange(val) {
      const id = val.zhiLiaoXMID
      const res = await getKangFuZLYZ({
        kangFuXMID: id
      })
      this.btName = res.data.yiZhuMC
      this.tableData2 = res.data.pingGuMX
      this.tableData3 = res.data.yiZhuZXMX
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;

  padding: 12px;
  background-color: #fff;
}
.content-left {
  min-height: 0;
  display: flex;
  flex-direction: column;
  background-color: #eaf0f9;
  padding: 14px;
}
.content-right {
  margin-left: 10px;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  background-color: #eaf0f9;
  padding: 14px;
  .right-bottom {
    margin-top: 25px;
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    .table {
      width: max-content;
      overflow: auto;
    }
  }
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 14px;
  .title {
    position: relative;
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 9px;
  }
  .title::before {
    position: absolute;
    left: -9px;
    width: 3px;
    height: 14px;
    content: '';
    background-color: #356ac5;
  }
}
.table {
  flex: 1;
  overflow: auto;
  //width: 702px;
  //max-width: 800px;
  ::v-deep .el-tag {
    border-color: #356ac5;
  }
}
::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
</style>
