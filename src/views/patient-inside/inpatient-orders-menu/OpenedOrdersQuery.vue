<template>
  <div class="container">
    <div class="header">
      <div class="query-word">开医嘱日期：</div>
      <div class="query-value">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          value-format="yyyy-MM-dd HH:mm:ss"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </div>
      <div class="query-word">医嘱类型：</div>
      <div class="query-value" style="margin-right: 0">
        <el-checkbox
          v-model="checkAll"
          :indeterminate="isIndeterminate"
          @change="handleCheckAllChange"
        >
          全选
        </el-checkbox>
      </div>
      <div class="query-value">
        <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange">
          <el-checkbox v-for="city in cities" :key="city" :label="city">{{ city }}</el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="query-word">排序：</div>
      <div class="query-value">
        <el-select v-model="paiXuMS" placeholder="请选择" style="min-width: 110px">
          <el-option label="医嘱时间" value="1" />
          <el-option label="医嘱类型" value="2" />
        </el-select>
      </div>
      <div class="button">
        <el-button type="primary" @click="getYkYz(0)">查询所有医嘱</el-button>
        <el-button type="primary" @click="getYkYz(1)">查询长期医嘱</el-button>
        <el-button type="primary" @click="getYkYz(2)">查询临时医嘱</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">已开医嘱查询</div>
      </div>
      <div class="table">
        <el-table
          border
          size="mini"
          :data="tableData"
          height="100%"
          max-height="560px"
          :span-method="objectSpanMethod"
        >
          <el-table-column prop="yiZhuLXMC" label="医嘱类型"></el-table-column>
          <el-table-column prop="yiZhuSJ" width="150" label="开医嘱时间"></el-table-column>
          <el-table-column prop="yiZhuNR" width="300" label="医嘱内容">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.yiZhuLX === '5' || scope.row.yiZhuLX === '10'"
                type="text"
                @click="getSqd(scope.row.yiZhuID)"
              >
                {{ scope.row.yiZhuNR }}
              </el-button>
              <div v-else v-html="scope.row.yiZhuNR"></div>
            </template>
          </el-table-column>
          <el-table-column prop="tianShu" label="已用天数"></el-table-column>
          <el-table-column prop="yiShengQM" label="医师签名"></el-table-column>
          <el-table-column prop="jiHuaKSSJ" width="150" label="预计执行时间"></el-table-column>
          <el-table-column prop="chiXuTS" label="持续天数"></el-table-column>
          <el-table-column prop="daoChuRY" label="导出护士"></el-table-column>
          <el-table-column prop="daoChuSJ" width="150" label="导出时间"></el-table-column>
          <el-table-column prop="yiZhuZT" label="医嘱状态">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.yiZhuZT === '1'">正常</el-tag>
              <el-tag v-else-if="scope.row.yiZhuZT === '0'" type="danger">已停止</el-tag>
              <el-tag v-else>其他</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="caoZuo" width="160" label="操作" align="center">
            <template slot-scope="scope">
              <el-button v-if="scope.row.zuHao === null" type="text" size="medium" disabled>
                查看
              </el-button>
              <el-button v-else type="text" size="medium" @click="getCheckReportData(scope.row)">
                查看
              </el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button v-if="scope.row.biHuanDZ === null" type="text" size="medium" disabled>
                闭环
              </el-button>
              <el-button v-else type="text" size="medium" @click="gotoBh(scope.row.biHuanDZ)">
                闭环
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible" width="600px" :before-close="handleClose">
      <div slot="title" class="dialog-title">
        <i class="el-icon-coin"></i>
        <span style="margin-left: 5px">已开医嘱查询查看</span>
      </div>
      <div class="dialog-body">
        <el-table border size="mini" :data="tableData2" height="100%" max-height="400px">
          <!--          <el-table-column prop="yiZhuNR" label="医嘱内容"></el-table-column>-->
          <el-table-column prop="zhiXingRYXM" label="执行人"></el-table-column>
          <el-table-column prop="zhiXingSJ" label="执行时间"></el-table-column>
        </el-table>
      </div>
      <div class="bottom-btn-group">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleClose">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getAdmissionList,
  getCheckReportDataBySqdIdList,
  getSqdShowUrlById,
  getYiZhuZXJLByZH,
  getZhiXingJLByYZID
} from '@/api/inpatient-order'
import { format } from 'date-fns'

const cityOptions = ['化验医嘱', '药品医嘱', '申请单', '治疗医嘱', '营养食品']
export default {
  name: 'OpenedOrdersQuery',
  data() {
    return {
      dialogVisible: false,
      nowRow: {},
      tableData: [],
      tableData2: [],
      dateRange: [
        format(new Date().setDate(new Date().getDay() - 7), 'yyyy-MM-dd HH:mm:ss'),
        format(new Date(), 'yyyy-MM-dd HH:mm:ss')
      ],
      checkAll: true,
      checkedCities: ['化验医嘱', '药品医嘱', '申请单', '治疗医嘱', '营养食品'],
      cities: cityOptions,
      isIndeterminate: false,
      paiXuMS: '1',
      nowYiZhuLXMC: '',
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setDate(start.getDay() - 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setMonth(start.getMonth() - 1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一年',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setFullYear(start.getFullYear() - 1)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.getYkYz(0)
  },
  methods: {
    handleCheckAllChange(val) {
      this.checkedCities = val ? cityOptions : []
      this.isIndeterminate = false
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.cities.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length
    },
    async getSqd(sqdId) {
      const res = await getSqdShowUrlById({
        shenQingDanID: sqdId
      })
      const url = res.data
      const newWindow = window.open(url, '_blank', 'width=800,height=600,scrollbars=yes')
    },
    async getCheckReportData(row) {
      let leiBie = 0
      switch (row.yiZhuLX) {
        case '0':
          leiBie = 5
          break
        case '1':
          leiBie = 1
          break
        case '2':
          leiBie = 1
          break
        case '4':
          leiBie = 4
          break
        case '6':
          leiBie = 2
          break
        case '7':
          leiBie = 2
          break
      }
      const res = await getYiZhuZXJLByZH({
        leiBie: leiBie,
        yiZhuZH: row.zuHao
      })
      this.tableData2 = res.data
      this.dialogVisible = true
    },
    async gotoBh(url) {
      const newWindow = window.open(url)
    },
    handleClose() {
      this.dialogVisible = false
    },
    async getYkYz(chaXunMS) {
      let yiZhuLBs = ''
      const yiZhuLBsMap = {
        化验医嘱: '2',
        药品医嘱: '1',
        申请单: '3',
        治疗医嘱: '4',
        营养食品: '5'
      }
      for (const d of this.checkedCities) {
        yiZhuLBs += yiZhuLBsMap[d] + ','
      }
      yiZhuLBs = yiZhuLBs.slice(0, -1)
      const res = await getAdmissionList({
        bingLiID: this.bingLiID,
        chaXunMS: String(chaXunMS),
        kaiShiSJ: this.dateRange[0],
        jieShuSJ: this.dateRange[1],
        paiXuMS: this.paiXuMS,
        yiZhuLBs: yiZhuLBs
      })
      if (res.hasError === 0) {
        for (let i = 0; i < res.data.length; i++) {
          res.data[i]['rowspan'] = 1
          for (let j = i + 1; j < res.data.length; j++) {
            if (res.data[i]['yiZhuLXMC'] === res.data[j]['yiZhuLXMC']) {
              res.data[i]['rowspan'] += 1
              res.data[j]['rowspan'] = 0
              if (j === res.data.length - 1) {
                i = j
              }
            } else {
              i = j - 1
              break
            }
          }
        }
        this.tableData = res.data
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (row.rowspan !== 0) {
          return {
            rowspan: row.rowspan,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }
  .query-value {
    white-space: nowrap;
    margin-right: 10px;
    ::v-deep .el-checkbox {
      margin-right: 5px;
      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}
.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }
  .table {
    flex: 1;
    overflow: auto;
  }
}
::v-deep .el-dialog__body {
  padding: 10px 20px 10px;
}
.dialog-body {
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
    padding: 10px;
  }
  .info-label {
    padding: 3px;
    text-align: right;
    width: 160px;
    background-color: #eaf0f9;
  }
  .info-value {
    padding: 10px;
    width: 30vw;
  }
}
.bottom-btn-group {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 30px;
  ::v-deep .el-button {
    margin-left: 10px;
  }
}
</style>
