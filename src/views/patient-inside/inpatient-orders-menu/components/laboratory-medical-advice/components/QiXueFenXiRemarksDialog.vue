<template>
  <el-dialog
    :visible="dialogRef.visible"
    :append-to-body="true"
    width="600px"
    :show-close="false"
    @open="initFormData()"
  >
    <span slot="title">
      <span class="dialog-title">
        <i class="el-icon-menu"></i>
        化验备注
      </span>
    </span>
    <div v-if="formData" class="dialog-component">
      体温35-42，小数点后一位；
      吸氧0.21-1，小数点后两位【与L之间转换公式：吸氧量[L/min]*4/100+0.21】!
      <div class="dialog-input">
        <el-form ref="formData" label-position="top" :model="formData" :rules="rules">
          <el-form-item label="体温:" prop="TW">
            <el-input-number v-model="formData.TW" :min="35" :max="42" :step="0.1" />
          </el-form-item>
          <el-form-item
            label="吸氧: (输入值大于1默认为吸氧量将进行自动计算,小于1默认为输入吸氧结果)"
            prop="XY"
          >
            <el-input-number v-model="formData.XY" :min="0.21" :max="1" :step="0.01" />
          </el-form-item>
          <el-form-item label="备注:" prop="BZ">
            <el-input
              v-model="formData.BZ"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 认</el-button>
      <el-button v-if="dialogRef.data !== ''" @click="dialogRef.resolve(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils'

export default {
  name: 'RemarksDialog',
  props: {
    dialogRef: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: null,
      rules: {
        TW: [{ required: true, message: '请输入体温', trigger: 'blur' }],
        XY: [{ required: true, message: '请输入吸氧', trigger: 'blur' }]
      }
    }
  },
  methods: {
    initFormData() {
      this.formData = {
        TW: null, //体温
        XY: null, //吸氧
        BZ: '' //备注
      }
      if (this.dialogRef.data) {
        let yzbzAll = this.dialogRef.data.split(' ')
        let fjbz = []
        yzbzAll.map((e) => {
          const item = e.split(':')
          if (item[0] === '体温') {
            this.formData.TW = item[1]
          } else if (item[0] === '吸氧') {
            this.formData.XY = item[1]
          } else if (item[0] === '备注') {
            this.formData.BZ = item[1]
          }
        })
      }
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.dialogRef.resolve(
            `体温:${this.formData.TW} 吸氧:${this.formData.XY} 备注:${this.formData.BZ}`
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-title {
  font-size: 16px;
}
.dialog-component {
  padding: 0 30px;
}
.dialog-input {
  margin-top: 10px;
  .el-input {
    width: 65%;
    margin-left: 10px;
  }
}

:deep(.el-dialog__footer) {
  border-top: none;
}
</style>
