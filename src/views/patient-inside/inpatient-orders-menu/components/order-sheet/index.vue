<template>
  <div class="container">
    <div class="header">
      <!-- 选择日期 -->
      <div class="query-word">时间范围：</div>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        @change="onChangeDate"
      ></el-date-picker>
      <!-- 审批状态 -->
      <div class="query-word">医嘱类别：</div>
      <div>
        <el-checkbox-group v-model="checked" @change="handleCheckChange">
          <el-checkbox label="1">长期医嘱</el-checkbox>
          <el-checkbox label="2">临时医嘱</el-checkbox>
        </el-checkbox-group>
      </div>
      <!-- 按钮操作 -->
      <el-button type="primary" class="purple-button" @click="searchFun">查询</el-button>
      <el-button type="primary" @click="printFun">打印</el-button>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">医嘱单查询结果</div>
      </div>
      <div ref="printContent" class="table">
        <el-table
          :data="tableData"
          :header-cell-style="{
            'background-color': '#EFF3FB',
            padding: '2px 0'
          }"
          max-height="500"
          border
          :span-method="handleSpanMethod"
          stripe
          style="width: 100%"
        >
          <!-- 开始 -->
          <el-table-column label="开始" header-align="center">
            <el-table-column prop="yiZhuSJ_RQ" label="日期" width="100"></el-table-column>
            <el-table-column prop="yiZhuSJ_SJ" label="时间" width="85"></el-table-column>
          </el-table-column>
          <el-table-column prop="yiZhuNR" label="医嘱内容">
            <template #default="scope">
              <div v-html="scope.row.yiZhuNR"></div>
            </template>
          </el-table-column>
          <el-table-column
            prop="yiShengQM"
            label="医师签名"
            align="center"
            header-align="center"
            width="90"
          >
            <template #default="scope">
              <img
                v-if="scope.row.yiShengQM"
                :src="scope.row.yiShengQM"
                style="width: 70px; object-fit: cover"
              />
            </template>
          </el-table-column>
          <el-table-column prop="zxsj" label="执行时间"></el-table-column>
          <el-table-column
            prop="daoChuRYQM"
            label="护士签名"
            align="center"
            header-align="center"
            width="90"
          >
            <template #default="scope">
              <img
                v-if="scope.row.daoChuRYQM"
                :src="scope.row.daoChuRYQM"
                style="width: 70px; object-fit: cover"
              />
            </template>
          </el-table-column>
          <!-- 停止 -->
          <el-table-column label="停止" align="center">
            <el-table-column prop="tingZhiSJ_RQ" label="日期" width="115"></el-table-column>
            <el-table-column prop="tingZhiSJ_SJ" label="时间" width="85"></el-table-column>
            <el-table-column
              prop="tingZhiYSQM"
              label="医师签名"
              align="center"
              header-align="center"
              width="90"
            >
              <template #default="scope">
                <img
                  v-if="scope.row.tingZhiYSQM"
                  :src="scope.row.tingZhiYSQM"
                  style="width: 70px; object-fit: cover"
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="tingZhiDCSJ_SJ"
              label="执行时间"
              width="85"
              header-align="center"
            ></el-table-column>
            <el-table-column
              prop="tingZhiDCRYQM"
              label="护士签名"
              align="center"
              header-align="center"
              width="90"
            >
              <template #default="scope">
                <img
                  v-if="scope.row.tingZhiDCRYQM"
                  :src="scope.row.tingZhiDCRYQM"
                  style="width: 70px; object-fit: cover"
                />
              </template>
            </el-table-column>
          </el-table-column>
          <!-- 审核 -->
          <el-table-column label="审核" align="center">
            <el-table-column prop="ca_SHSJ" label="日期时间" width="150"></el-table-column>
            <el-table-column
              prop="ca_SHRYQM"
              label="审核签名"
              align="center"
              header-align="center"
              width="90"
            >
              <template #default="scope">
                <img
                  v-if="scope.row.ca_SHRYQM"
                  :src="scope.row.ca_SHRYQM"
                  style="width: 70px; object-fit: cover"
                />
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { format, subMonths } from 'date-fns'
import { getYiZhuDan } from '@/api/doctors-advice'
export default {
  data() {
    return {
      tableData: [], //表格数据
      checked: ['1'], //医嘱类别选择项
      dateRange: '',
      dateS: '', //开始日期
      dateE: '' //结束日期
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    }
  },
  mounted() {
    this.dateS = format(subMonths(new Date(), 1), 'yyyy-MM-dd')
    this.dateE = format(new Date(), 'yyyy-MM-dd')
    // this.dateS = '2023-07-08',
    // this.dateE = '2023-07-12',
    this.getYiZhuDan()
  },
  methods: {
    // 合并相同项
    handleSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 只合并 ID 列（columnIndex === 0）
      if (columnIndex === 0) {
        // 1. 找到当前行的 ID
        const currentId = row.yiZhuSJ_RQ
        // 2. 计算当前 ID 在数据中的起始行和结束行
        const startRow = this.tableData.findIndex((item) => item.yiZhuSJ_RQ === currentId)
        const endRow = this.tableData.findLastIndex((item) => item.yiZhuSJ_RQ === currentId)
        // 3. 如果是当前 ID 的第一行，则合并
        if (rowIndex === startRow) {
          return {
            rowspan: endRow - startRow + 1, // 合并的行数
            colspan: 1 // 合并的列数（1 表示不跨列）
          }
        } else {
          return {
            rowspan: 0, // 0 表示不显示
            colspan: 0
          }
        }
      }
    },
    // 医嘱类别勾选
    handleCheckChange(newVal) {
      if (newVal.length > 1) {
        this.checked = [newVal[newVal.length - 1]]
      }
    },
    // 获取医嘱单
    async getYiZhuDan() {
      try {
        const res = await getYiZhuDan({
          bingLiID: this.bingLiID,
          chaXunMS: this.checked[0],
          kaiShiSJ: this.dateS,
          jieShuSJ: this.dateE
        })
        if (res.hasError === 0) {
          // 通用日期格式化工具函数
          const formatDateSafely = (dateValue, pattern = 'yyyy-MM-dd') => {
            if (
              dateValue === null ||
              dateValue === undefined ||
              dateValue === '' ||
              isNaN(new Date(dateValue).getTime())
            )
              return ''
            return format(new Date(dateValue), pattern)
          }
          this.tableData = res.data
          // 使用
          this.tableData = this.tableData.map((item) => ({
            ...item,
            yiZhuSJ_RQ: formatDateSafely(item.yiZhuSJ_RQ, 'yyyy-MM-dd'),
            yiZhuSJ_SJ: format(new Date(`1970-01-01T${item.yiZhuSJ_SJ}`), 'HH:mm'),
            tingZhiSJ_RQ: formatDateSafely(item.tingZhiSJ_RQ, 'yyyy-MM-dd'),
            zxsj: this.checked[0] == 1 ? item.daoChuSJ_SJ : item.zhiXingSJ_SJ || item.daoChuSJ_SJ
          }))
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 日期选择
    onChangeDate(e) {
      this.dateS = e[0]
      this.dateE = e[1]
    },
    // 查询
    async searchFun() {
      this.getYiZhuDan()
    },
    // 打印
    printFun() {
      const contentHtml = this.$refs.printContent.innerHTML
      const printWindow = window.open('', '_blank')
      printWindow.document.write(`
        <html>
          <head>
            <title>打印内容</title>
            <style>
              body { font-family: Arial, sans-serif; }
              table { border-collapse: collapse; width: 100%; }
              th, td { border: 1px solid #ddd; padding: 8px; }
            </style>
          </head>
          <body>
            <h1 style="text-align: center;">温州医科大学附属第一医院</h1>
            <h2 style="text-align: center;">医嘱单</h2>
            ${contentHtml}
          </body>
        </html>
      `)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  // padding: 12px;
  // background-color: #fff;
  background-color: #eff3fb;

  // height: 200vh; /* 确保根容器占满屏幕 */
  // display: flex;
  // flex-direction: column;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  // margin-bottom: 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  margin: 12px 0 5px;
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    // margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    // background-color: #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  // background-color: #eff3fb;
  height: 730px;
  padding: 14px 0px;

  // flex: 1; /* 占据剩余空间 */
  // overflow: hidden; /* 禁止外层滚动 */
  // display: flex;
  // flex-direction: column;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 1476px;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }
  .table {
    ::v-deep .el-button {
      // color: #356ac5;
    }
  }
}

.purple-button {
  background: #a66dd4;
  border: 1px solid #a66dd4;
  &:hover,
  &:focus {
    background: #a66dd4;
    border-color: #a66dd4;
  }
}

::v-deep .el-checkbox {
  margin-right: 10px;
}
</style>
