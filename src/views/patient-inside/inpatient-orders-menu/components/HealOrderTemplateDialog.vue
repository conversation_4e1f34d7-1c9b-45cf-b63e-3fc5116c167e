<template>
  <default-dialog :visible.sync="isVisible" title="治疗医嘱模板" width="1300px" pop-type="tip">
    <div class="heal-template-dialog-content">
      <div class="left-panel">
        <el-menu :default-openeds="openedMenus" class="template-menu" @open="handleMenuOpen">
          <!-- 个人模板 -->
          <el-submenu index="personal">
            <template #title>
              <span>个人模板</span>
            </template>
            <!-- 个人模板下的目录 -->
            <template v-if="personalCategories.length > 0">
              <el-submenu
                v-for="category in personalCategories"
                :key="category.moBanID"
                :index="category.moBanID + ''"
              >
                <template #title>{{ category.mingCheng }}</template>
                <!-- 第三级菜单项 -->
                <template v-if="category.templates && category.templates.length > 0">
                  <el-menu-item
                    v-for="template in category.templates"
                    :key="template.yiZhuMLID"
                    :index="template.yiZhuMLID + ''"
                    @click="handleTemplateClick(category, template)"
                  >
                    <div class="menu-item-with-checkbox">
                      <el-checkbox
                        :value="isChecked(category.moBanID, template.yiZhuMLID)"
                        @change="(val) => handleCheckboxChange(val, category, template)"
                        @click.native.stop
                      ></el-checkbox>
                      <span>{{ template.yiZhuMLMC }}</span>
                    </div>
                  </el-menu-item>
                </template>
                <el-menu-item v-else disabled>暂无数据</el-menu-item>
              </el-submenu>
            </template>
            <el-menu-item v-else class="empty-item">暂无个人模板</el-menu-item>
          </el-submenu>

          <!-- 专科模板 -->
          <el-submenu index="department">
            <template #title>
              <span>专科模板</span>
            </template>
            <!-- 专科模板分类(第二级) -->
            <template v-if="departmentCategories.length > 0">
              <el-submenu
                v-for="category in departmentCategories"
                :key="category.moBanID"
                :index="category.moBanID + ''"
              >
                <template #title>{{ category.mingCheng }}</template>
                <!-- 第三级菜单项 -->
                <template v-if="category.templates && category.templates.length > 0">
                  <el-menu-item
                    v-for="template in category.templates"
                    :key="template.yiZhuMLID"
                    :index="template.yiZhuMLID + ''"
                    @click="handleTemplateClick(category, template)"
                  >
                    <div class="menu-item-with-checkbox">
                      <el-checkbox
                        :value="isChecked(category.moBanID, template.yiZhuMLID)"
                        @change="(val) => handleCheckboxChange(val, category, template)"
                        @click.native.stop
                      ></el-checkbox>
                      <span>{{ template.yiZhuMLMC }}</span>
                    </div>
                  </el-menu-item>
                </template>
                <el-menu-item v-else disabled>暂无数据</el-menu-item>
              </el-submenu>
            </template>
            <el-menu-item v-else class="empty-item">暂无专科模板</el-menu-item>
          </el-submenu>
        </el-menu>
      </div>
      <div class="right-panel">
        <div class="table-title">
          <span>已选择的医嘱模板</span>
          <el-button type="primary" size="mini" @click="handleConfirm">确认</el-button>
        </div>
        <div class="table-wrapper">
          <el-table :data="selectedOrders" border stripe size="mini" height="440px">
            <el-table-column
              prop="muBanMC"
              label="模板名称"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              prop="yiZhuMLMC"
              label="医嘱名称"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column label="数量" width="68">
              <template slot-scope="scope">
                <el-input v-model.number="scope.row.shuLiang" type="number" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="持续天数" width="100">
              <template slot-scope="scope">
                <el-input v-model.number="scope.row.chiXuTS" type="number" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="执行频率" width="100">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.zhiXingPL"
                  placeholder="请选择"
                  size="mini"
                  clearable
                  filterable
                  allow-create
                  default-first-option
                >
                  <el-option
                    v-for="item in zhiXingPLOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="执行方法" width="100">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.zhiXingFF"
                  placeholder="请选择"
                  size="mini"
                  clearable
                  filterable
                  allow-create
                  default-first-option
                >
                  <el-option
                    v-for="item in zhiXingFFOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="特殊方法" width="100">
              <template slot-scope="scope">
                <el-input v-model="scope.row.teShuSM" size="mini" placeholder="请输入"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="计划开始时间" width="190">
              <template slot-scope="scope">
                <el-date-picker
                  v-model="scope.row.kaiShiSJ"
                  type="datetime"
                  placeholder="选择日期时间"
                  size="mini"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"
                ></el-date-picker>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </default-dialog>
</template>

<script>
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import { zhiLiaoMbInit, getZhiLiaoMbZuHe } from '@/api/inpatient-order'
import { format } from 'date-fns'

export default {
  name: 'HealOrderTemplateDialog',
  components: {
    DefaultDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    inpatientInit: {
      type: Object,
      default: () => ({})
    },
    tabActive: {
      // 当前标签页('cq'长期或'ls'临时)
      type: String,
      required: true
    }
  },
  data() {
    return {
      personalCategories: [], // 个人模板目录
      departmentCategories: [], // 专科模板目录
      checkedTemplates: {}, // 保存已勾选的模板
      selectedOrders: [], // 已选择的医嘱列表
      openedMenus: ['personal', 'department'], // 当前展开的菜单
      currentLoading: null, // 当前正在加载的菜单ID
      zhiXingPLOptions: [
        // 频率选项
      ],
      zhiXingFFOptions: [
        // 执行方法选项
      ]
    }
  },
  computed: {
    isVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    bingLiID() {
      return this.inpatientInit?.ezyblbrVo?.bingLiID || '' // 从初始化数据获取病历ID
    }
  },
  watch: {
    isVisible(val) {
      if (val) {
        this.resetData()
        this.fetchTemplateTree()
      }
    }
  },
  methods: {
    // 重置数据
    resetData() {
      this.personalCategories = []
      this.departmentCategories = []
      this.checkedTemplates = {}
      this.selectedOrders = []
      this.openedMenus = ['personal', 'department']
      this.currentLoading = null
    },

    // 构建模板树结构 - 处理第一、二级菜单
    processTemplateData(data) {
      if (!data) return

      // 处理个人模板二级分类
      if (data.geRenMbs && Array.isArray(data.geRenMbs)) {
        this.personalCategories = data.geRenMbs.map((item) => ({
          ...item,
          templates: [] // 初始化三级模板数据数组
        }))
      }

      // 处理专科模板二级分类
      if (data.zhuanKeMbs && Array.isArray(data.zhuanKeMbs)) {
        this.departmentCategories = data.zhuanKeMbs.map((item) => ({
          ...item,
          templates: [] // 初始化三级模板数据数组
        }))
      }
    },

    // 获取模板目录(一、二级)
    async fetchTemplateTree() {
      try {
        const res = await zhiLiaoMbInit()
        if (res.hasError === 0 && res.data) {
          this.processTemplateData(res.data)
          this.zhiXingPLOptions = res.data.zhiXingPLs.map((item) => ({
            value: item.pinLuDM,
            label: item.pinLuMC
          }))
          this.zhiXingFFOptions = res.data.zhiXingFFs.map((item) => ({
            value: item.fangFaDM,
            label: item.fangFaMC
          }))
        } else {
          this.$message.warning('获取模板目录失败：' + (res.errorMessage || '未知错误'))
        }
      } catch (error) {
        console.error('获取治疗模板目录失败:', error)
        this.$message.error(`获取治疗模板目录失败: ${error.message || error}`)
      }
    },

    // 处理菜单打开事件
    handleMenuOpen(index) {
      // 检查是否为二级菜单(非顶级菜单)
      if (index !== 'personal' && index !== 'department') {
        // 确保菜单保持打开状态
        if (!this.openedMenus.includes(index)) {
          this.openedMenus.push(index)
        }

        // 如果已经在加载中，或者已经加载过，则跳过
        if (this.currentLoading === index) return

        // 查找对应的分类
        let category = this.findCategoryById(index)
        if (category && (!category.templates || category.templates.length === 0)) {
          this.loadTemplateDetails(category)
        }
      }
    },

    // 根据ID查找分类
    findCategoryById(id) {
      // 尝试在个人模板中查找
      let category = this.personalCategories.find((item) => item.moBanID == id)
      if (!category) {
        // 如果没找到，尝试在专科模板中查找
        category = this.departmentCategories.find((item) => item.moBanID == id)
      }
      return category
    },

    // 加载模板详情(第三级)
    async loadTemplateDetails(category) {
      if (!category || !category.moBanID) return

      // 如果已经加载过且有数据，不重复加载
      if (category.templates && category.templates.length > 0) return

      // 标记当前正在加载的菜单
      this.currentLoading = category.moBanID + ''
      // 确保菜单保持打开状态
      if (!this.openedMenus.includes(category.moBanID + '')) {
        this.openedMenus.push(category.moBanID + '')
      }
      try {
        // 获取模板组合数据
        const res = await getZhiLiaoMbZuHe({
          muBanID: category.moBanID
        })

        if (res.hasError === 0 && Array.isArray(res.data)) {
          // 更新三级数据
          this.$set(category, 'templates', res.data)

          // 初始化该分类的勾选集合
          if (!this.checkedTemplates[category.moBanID]) {
            this.$set(this.checkedTemplates, category.moBanID, new Set())
          }
        } else {
          this.$set(category, 'templates', [])
        }
      } catch (error) {
        this.$message.error(`获取模板详情失败: ${error.message || error}`)
        this.$set(category, 'templates', [])
      } finally {
        // 清除当前加载标记
        this.currentLoading = null
      }
    },

    // 判断模板是否被勾选
    isChecked(moBanID, yiZhuMLID) {
      return this.checkedTemplates[moBanID] && this.checkedTemplates[moBanID].has(yiZhuMLID)
    },

    // 处理三级菜单项点击
    handleTemplateClick(category, template) {
      // 点击菜单项时切换勾选状态
      const isCurrentlyChecked = this.isChecked(category.moBanID, template.yiZhuMLID)
      this.handleCheckboxChange(!isCurrentlyChecked, category, template)
    },

    // 处理复选框变化
    handleCheckboxChange(checked, category, template) {
      if (!category || !category.moBanID || !template || !template.yiZhuMLID) return

      // 确保该分类的勾选集合已初始化
      if (!this.checkedTemplates[category.moBanID]) {
        this.$set(this.checkedTemplates, category.moBanID, new Set())
      }

      // 获取当前时间，用于统一更新所有医嘱的开始时间
      const currentTime = format(new Date(), 'yyyy-MM-dd HH:mm:ss')

      if (checked) {
        // 添加到已勾选集合
        this.checkedTemplates[category.moBanID].add(template.yiZhuMLID)

        // 添加到已选医嘱列表
        const newOrder = {
          ...template,
          muBanMC: category.mingCheng, // 模板名称
          muBanID: category.moBanID, // 模板ID
          shuLiang: template.shuLiang || '',
          chiXuTS: template.chiXuTS || '999', // 持续天数
          zhiXingPL: template.zhiXingPL || '',
          zhiXingFF: template.zhiXingFF || '',
          teShuSM: template.teShuSM || '',
          kaiShiSJ: currentTime
        }

        this.selectedOrders.push(newOrder)
      } else {
        // 从勾选集合中移除
        this.checkedTemplates[category.moBanID].delete(template.yiZhuMLID)

        // 从已选医嘱列表中移除
        this.selectedOrders = this.selectedOrders.filter(
          (order) => !(order.muBanID === category.moBanID && order.yiZhuMLID === template.yiZhuMLID)
        )
      }

      // 更新所有已选医嘱的开始时间为当前时间
      this.selectedOrders.forEach((order) => {
        order.kaiShiSJ = currentTime
      })
    },
    handleConfirm() {
      this.$emit('confirm', this.selectedOrders)
    }
  }
}
</script>

<style scoped lang="scss">
.heal-template-dialog-content {
  display: flex;
  height: 500px;

  .left-panel {
    width: 220px;
    margin-right: 10px;
    overflow-y: auto;
    height: 100%;

    ::v-deep .el-submenu__title {
      height: 40px;
      line-height: 40px;
      font-weight: 600;
      font-size: 14px !important;
      background-color: transparent;
      padding: 0 20px !important;
      border-bottom: 1px solid #dcdfe6;

      &:hover {
        background-color: #ecf5ff;
      }
    }

    .template-menu {
      height: 100%;
      border: 1px solid #dcdfe6;
      background-color: transparent;

      & > .el-submenu > ::v-deep .el-submenu__title {
        border-bottom: 1px solid #dcdfe6;
        background-color: #eaf0f9;
      }

      // 空列表提示
      .empty-item {
        color: #909399;
        font-style: italic;
      }

      // 菜单项与复选框组合样式
      .menu-item-with-checkbox {
        display: flex;
        align-items: center;
        width: 100%;

        .el-checkbox {
          margin-right: 5px;
        }

        span {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px !important;
        }
      }

      ::v-deep .el-menu-item {
        height: 36px;
        line-height: 36px;
        background-color: transparent;
        border-bottom: 1px solid #dcdfe6;
        padding: 0 20px !important;

        &:hover {
          background-color: #ecf5ff;
        }

        &.is-active {
          color: #409eff;
          background-color: #ecf5ff;
        }
      }
    }
  }

  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;

    .table-title {
      font-weight: bold;
      padding: 0 12px;
      height: 40px;
      background-color: #fafbfc;
      border: 1px solid #dcdfe6;
      display: flex;
      align-items: center;
      justify-content: space-between;

      span {
        display: inline-block;
        padding-left: 5px;
        border-left: 3px solid #409eff;
      }
    }

    .table-wrapper {
      flex-grow: 1;
      border: 1px solid #dcdfe6;
      border-top: none;
      padding: 12px;
    }

    ::v-deep .el-table {
      width: 100%;
      .el-date-editor.el-input,
      .el-date-editor.el-input__inner,
      .el-select {
        width: 100%;
      }
      td,
      th {
        padding: 4px 0;
      }
      .cell {
        padding-left: 5px;
        padding-right: 5px;
      }
    }
  }
}
</style>
