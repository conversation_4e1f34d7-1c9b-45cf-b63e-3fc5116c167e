<template>
  <div class="container">
    <div class="content">
      <div class="content-header">
        <div class="title">检验检查医嘱执行情况</div>
      </div>
      <div class="table">
        <el-table style="width: 710px" border stripe size="mini" :data="tableData">
          <el-table-column prop="yiZhuSJ" width="220" label="检查时间"></el-table-column>
          <el-table-column prop="xiangMuMC" width="400" label="检查名称"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" label="完成情况" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.zhuangTaiBZ === 1">未完成</el-tag>
              <el-tag v-else>完成</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getUnFinishedExamDataAll } from '@/api/inpatient-order'

export default {
  name: 'VerifyCheckOrdersStatus',
  data() {
    return {
      tableData: []
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.getUnFinishedExam()
  },
  methods: {
    async getUnFinishedExam() {
      const res = await getUnFinishedExamDataAll({
        bingLiID: this.bingLiID
      })
      this.tableData = res.data
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }
  .query-value {
    white-space: nowrap;
    margin-right: 10px;
    ::v-deep .el-checkbox {
      margin-right: 5px;
      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}
.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }
  .table {
    flex: 1;
    overflow: auto;
    //width: 702px;
    //max-width: 800px;
    ::v-deep .el-tag {
      border-color: #356ac5;
    }
  }
}
::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
</style>
