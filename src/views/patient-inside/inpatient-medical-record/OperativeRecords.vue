<template>
  <!-- 手术相关记录 -->
  <div class="operative-records-container">
    <div class="operative-records-layout">
      <!-- 左侧列表 -->
      <div class="left-panel">
        <!-- 标题和手术次序选择 -->
        <div class="left-header">
          <div class="header-content">
            <div class="title-area">
              <span>手术相关记录</span>
            </div>
            <div class="surgery-order-area">
              <el-select
                v-model="selectedSurgeryOrder"
                placeholder="选择手术次序"
                size="mini"
                style="width: 150px"
                @change="handleSurgeryOrderChange"
              >
                <el-option
                  v-for="order in surgeryOrderOptions"
                  :key="order.value"
                  :label="order.label"
                  :value="order.value"
                ></el-option>
              </el-select>
              <el-button type="primary" @click="handleAddSurgeryOrder">新增手术次序</el-button>
            </div>
          </div>
        </div>

        <!-- 上方：可书写记录列表 -->
        <div class="writable-records-section">
          <div class="section-header">
            <span class="section-title">可书写记录</span>
          </div>
          <div class="writable-records-list">
            <div class="writable-record-item" @click="handleStaticRecord">
              <span class="record-name">手术室外安全检查表（手麻）</span>
            </div>
            <div
              v-for="(record, index) in sortedWritableRecords"
              :key="`writable-${index}`"
              class="writable-record-item"
              @click="handleWritableRecordClick(record)"
            >
              <span>【新增】</span>
              <span class="record-name">{{ getRecordDisplayName(record) }}</span>
            </div>
          </div>
        </div>

        <!-- 下方：已书写记录列表（按手术次序分组） -->
        <div class="written-records-section">
          <div class="section-header">
            <span class="section-title">记录列表</span>
          </div>
          <div class="written-records-list">
            <el-collapse v-model="activeCollapseNames">
              <el-collapse-item
                v-for="group in groupedWrittenRecords"
                :key="group.surgeryOrder"
                :title="group.title"
                :name="group.surgeryOrder"
              >
                <div class="collapse-content">
                  <div
                    v-for="record in group.records"
                    :key="record.id"
                    class="written-record-item"
                    :class="{ active: currentRecord && currentRecord.id === record.id }"
                    @click="handleWrittenRecordClick(record)"
                  >
                    <div class="record-info">
                      <div class="record-name">{{ record.wenShuMC }}</div>
                      <div class="record-time">{{ record.jilluSJ }}</div>
                    </div>
                  </div>
                  <div v-if="group.records.length === 0" class="empty-group">
                    暂无记录，点击上方可书写记录进行添加
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
            <div v-if="groupedWrittenRecords.length === 0" class="empty-state">暂无已书写记录</div>
          </div>
        </div>
      </div>

      <!-- 右侧详情 -->
      <div class="right-panel">
        <div class="right-header">
          <div class="title">{{ currentRecord ? currentRecord.wenShuMC : '手术记录详情' }}</div>
          <div class="actions">
            <el-button v-if="currentRecord" type="primary" size="mini" @click="handleSaveClick">
              保存
            </el-button>
            <el-button v-if="currentRecord" type="primary" size="mini" @click="handleDeleteClick">
              删除
            </el-button>
            <el-button v-if="currentRecord" type="primary" size="mini" @click="handlePrintClick">
              打印
            </el-button>
          </div>
        </div>

        <!-- 内容区域 -->
        <div v-loading="loading" class="content" element-loading-text="加载中...">
          <iframe
            v-if="iframeUrl"
            ref="contentIframe"
            :src="iframeUrl"
            frameborder="0"
            @load="handleIframeLoad"
          ></iframe>
          <div v-else class="no-content">
            <i class="el-icon-document"></i>
            <p>请选择左侧记录查看详情</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 文书选择弹窗 -->
    <default-dialog
      :visible.sync="documentSelectDialog.visible"
      :title="documentSelectDialog.title"
      width="500px"
      pop-type="custom"
      @close="handleDocumentSelectCancel"
    >
      <div class="document-select-content">
        <div class="search-area">
          <el-input
            v-model="documentSearchKeyword"
            placeholder="请输入文书名称或拼音码进行搜索"
            size="small"
            clearable
            prefix-icon="el-icon-search"
          />
        </div>
        <div class="document-list">
          <div
            v-for="(doc, index) in filteredDocumentList"
            :key="`doc-${index}`"
            class="document-item"
            @click="handleDocumentItemClick(doc)"
          >
            <div class="document-info">
              <div class="document-name">{{ doc.mingChen }}</div>
            </div>
            <div class="document-action">
              <i class="el-icon-right"></i>
            </div>
          </div>
          <div v-if="filteredDocumentList.length === 0" class="empty-document">
            <i class="el-icon-document"></i>
            <p>{{ documentSearchKeyword ? '未找到匹配的文书' : '暂无可选择的文书' }}</p>
          </div>
        </div>
      </div>
    </default-dialog>

    <!-- 手术通知单选择弹窗 -->
    <default-dialog
      :visible.sync="notificationDialog.visible"
      :title="notificationDialog.title"
      width="1200px"
      pop-type="custom"
      @close="handleNotificationSelectCancel"
      @cancel="handleNotificationSelectCancel"
    >
      <div class="notification-select-content">
        <!-- 警告提示 -->
        <div class="warning-notice">
          <i class="el-icon-warning"></i>
          <span>注意：若关联后要改关联的手术，必须删除这份文书重写！！</span>
        </div>

        <!-- 手术通知单表格 -->
        <el-table
          :data="notificationList"
          style="width: 100%"
          height="400"
          highlight-current-row
          empty-text="暂无手术通知单"
          @row-click="handleNotificationSelect"
        >
          <el-table-column
            prop="zhuYuanHao"
            label="住院号"
            width="100"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="bingRenXM"
            label="病人姓名"
            width="100"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="kaiDanSJ"
            label="开单时间"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="niShouShuSJ"
            label="手术时间"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column label="主手术" width="150" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ (scope.row.shouShuXM && scope.row.shouShuXM.zhuShouShu) || '未知' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="zhuDaoYSXM"
            label="主刀医生"
            width="100"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="shiFouJZMC"
            label="是否急诊"
            width="80"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="zhuangTaiBZMC"
            label="状态标志"
            width="100"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="shenPiYJMC"
            label="审批意见"
            min-width="120"
            show-overflow-tooltip
          ></el-table-column>
        </el-table>
      </div>
    </default-dialog>
  </div>
</template>

<script>
import {
  getAllYiShuXieShouShuJiLuByBLID,
  getKeShuXieShouShuJLListByzhuanKeID,
  getWenShuGeShiListByWslx,
  checkTZDBind,
  getShouShuTZDByBLID
} from '@/api/operative-records'
import { mapState } from 'vuex'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import iframeCommunication from '@/utils/iframe-communication'

export default {
  name: 'OperativeRecords',
  components: {
    DefaultDialog
  },
  data() {
    return {
      // 已书写手术记录列表
      yiShuXieWenShuList: [],
      // 可书写手术记录列表（下拉框选项）
      keShuXieWenShuList: [],
      // 当前选中的记录
      currentRecord: null,
      // iframe URL
      iframeUrl: '',
      // 表格高度
      tableHeight: '100%',
      // 加载状态
      loading: false,
      // 基础URL
      baseUrl: 'http://************/ehr',
      // 选中的手术次序
      selectedSurgeryOrder: '',
      // 手术次序选项列表
      surgeryOrderOptions: [],
      // 折叠面板激活的名称
      activeCollapseNames: [],
      // 文书选择弹窗
      documentSelectDialog: {
        visible: false,
        title: '选择具体文书格式'
      },
      // 文书列表
      documentList: [],
      // 搜索关键词
      documentSearchKeyword: '',
      // 当前点击的可书写记录
      currentWritableRecord: null,
      // 通知单ID
      tongZhiDanID: '',
      // 手术通知单选择弹窗
      notificationDialog: {
        visible: false,
        title: '病人手术通知单关联'
      },
      // 手术通知单列表
      notificationList: [],
      // 选中的通知单
      selectedNotification: null,
      // 通知单选择Promise的resolve函数
      notificationSelectResolve: null
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientInfo: ({ patient }) => patient.patientInit,
      gongZhongDM: ({ patient }) => patient.doctorInfo.gongZhongDM_DZ, // 工种代码对照
      yongHuID: ({ user }) => user.yongHuID
    }),

    // 按排序字段排序的可书写记录列表
    sortedWritableRecords() {
      return [...this.keShuXieWenShuList].sort((a, b) => {
        const orderA = a.paiXu || 0
        const orderB = b.paiXu || 0
        return orderA - orderB
      })
    },

    // 按手术次序分组的已书写记录
    groupedWrittenRecords() {
      const groups = {}

      // 按手术次序分组
      this.yiShuXieWenShuList.forEach((record) => {
        const surgeryOrder = record.shoushuCX ? record.shoushuCX.toString() : '1'
        if (!groups[surgeryOrder]) {
          groups[surgeryOrder] = []
        }
        groups[surgeryOrder].push(record)
      })

      // 转换为数组格式并排序
      return Object.keys(groups)
        .sort((a, b) => parseInt(a) - parseInt(b))
        .map((surgeryOrder) => ({
          surgeryOrder,
          title: `第${surgeryOrder}次手术`,
          records: groups[surgeryOrder].sort((a, b) => {
            // 按记录时间排序
            return new Date(a.jilluSJ || 0) - new Date(b.jilluSJ || 0)
          })
        }))
    },

    // 过滤后的文书列表
    filteredDocumentList() {
      if (!this.documentSearchKeyword) {
        return this.documentList
      }
      const keyword = this.documentSearchKeyword.toLowerCase()
      return this.documentList.filter((doc) => {
        // 支持拼音码搜索和中文名称搜索
        const pinYinMatch = doc.pinYin && doc.pinYin.toLowerCase().includes(keyword)
        const nameMatch = doc.mingChen && doc.mingChen.toLowerCase().includes(keyword)
        return pinYinMatch || nameMatch
      })
    }
  },
  mounted() {
    // 初始化数据
    this.fetchInitData()
    // 初始化iframe通信
    iframeCommunication.init()
  },

  beforeDestroy() {
    // 销毁iframe通信
    iframeCommunication.destroy()
  },
  methods: {
    // 获取初始化数据
    async fetchInitData() {
      this.loading = true
      try {
        // 并行获取已书写记录和可书写记录
        const [yiShuXieRes, keShuXieRes] = await Promise.all([
          getAllYiShuXieShouShuJiLuByBLID({ bingLiID: this.bingLiID }),
          getKeShuXieShouShuJLListByzhuanKeID({ zhuanKeID: this.zhuanKeID })
        ])

        if (yiShuXieRes.hasError === 0) {
          this.yiShuXieWenShuList = yiShuXieRes.data || []
          // 初始化手术次序选项
          this.initSurgeryOrderOptions()
          // 设置折叠面板的初始展开状态
          this.initActiveCollapseNames()
        } else {
          this.$message.error(yiShuXieRes.errorMessage || '获取已书写记录失败')
        }

        if (keShuXieRes.hasError === 0) {
          this.keShuXieWenShuList = keShuXieRes.data || []
        } else {
          this.$message.error(keShuXieRes.errorMessage || '获取可书写记录失败')
        }
      } catch (error) {
        console.error('初始化失败', error)
        this.$message.error('初始化失败')
      } finally {
        this.loading = false
      }
    },

    // 初始化手术次序选项
    initSurgeryOrderOptions() {
      const orders = new Set()

      // 从已书写记录中提取手术次序
      this.yiShuXieWenShuList.forEach((record) => {
        const order = record.shoushuCX ? record.shoushuCX.toString() : '1'
        orders.add(order)
      })

      // 如果没有任何记录，默认添加第1次手术
      if (orders.size === 0) {
        orders.add('1')
      }

      // 转换为选项格式并排序
      this.surgeryOrderOptions = Array.from(orders)
        .sort((a, b) => parseInt(a) - parseInt(b))
        .map((order) => ({
          value: order,
          label: `第${order}次手术`
        }))

      // 设置默认选中第一个
      if (this.surgeryOrderOptions.length > 0 && !this.selectedSurgeryOrder) {
        this.selectedSurgeryOrder = this.surgeryOrderOptions[0].value
      }
    },

    // 初始化折叠面板展开状态
    initActiveCollapseNames() {
      // 如果已书写记录列表不为空，自动展开所有包含记录的手术次序
      if (this.yiShuXieWenShuList.length > 0) {
        // 获取所有手术次序并排序
        const orders = [
          ...new Set(
            this.yiShuXieWenShuList.map((record) =>
              record.shoushuCX ? record.shoushuCX.toString() : '1'
            )
          )
        ].sort((a, b) => parseInt(a) - parseInt(b))

        // 展开所有有记录的手术次序面板
        this.activeCollapseNames = [...orders]
      } else {
        // 如果没有记录，清空展开状态
        this.activeCollapseNames = []
      }
    },

    // 手术次序选择改变
    handleSurgeryOrderChange(value) {
      this.selectedSurgeryOrder = value
    },

    // 新增手术次序
    async handleAddSurgeryOrder() {
      // 检查病历状态
      if (this.patientInfo.bingLiZT === '1') {
        await this.$confirm('已封存病历不能进行该操作!', '提示信息', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'info'
        })
        return
      }

      // 已有手术记录时提示
      if (this.yiShuXieWenShuList.length > 0) {
        try {
          await this.$confirm('病人已有手术记录组，确认新增？', '提示信息', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          })
        } catch {
          return
        }
      }

      // 计算下一个手术次序
      const maxOrder = Math.max(...this.surgeryOrderOptions.map((opt) => parseInt(opt.value)), 0)
      const newOrder = (maxOrder + 1).toString()

      // 添加到选项列表
      this.surgeryOrderOptions.push({
        value: newOrder,
        label: `第${newOrder}次手术`
      })

      // 选中新增的手术次序
      this.selectedSurgeryOrder = newOrder
      this.yiShuXieWenShuList.push({
        shoushuCX: newOrder
      })
    },

    // 获取记录显示名称
    getRecordDisplayName(record) {
      // 当geShiDM为"0000"时显示leiXingMC字段，其他情况显示wenShuMC字段
      if (record.geShiDM === '0000') {
        return record.leiXingMC || record.wenShuMC || '未知文书'
      }
      return record.wenShuMC || '未知文书'
    },
    // 点击固定文书
    handleStaticRecord() {
      this.iframeUrl = `http://172.16.203.187/#/operatingroom/patient/documentlist?patientid=${this.patientInfo.bingAnHao}&registerid=${this.patientInfo.zhuYuanID}`
    },

    // 点击可书写记录
    async handleWritableRecordClick(record) {
      // 如果geShiDM为"0000"，需要弹出选择弹窗
      if (record.geShiDM === '0000') {
        this.currentWritableRecord = record
        await this.showDocumentSelectDialog(record)
      } else {
        this.createNewRecord(record)
      }
    },

    // 创建新记录
    async createNewRecord(record) {
      // 创建记录
      this.currentRecord = {
        ...record,
        id: 't' + new Date().getTime(),
        shoushuCX: this.selectedSurgeryOrder,
        jilluSJ: new Date().toLocaleString()
      }
      this.iframeUrl = this.getRecordEditUrl(this.currentRecord)
    },

    // 显示文书选择弹窗
    async showDocumentSelectDialog(record) {
      try {
        this.loading = true
        // 调用接口获取文书列表
        const response = await getWenShuGeShiListByWslx({
          wenShuLX: record.wenShuLX
        })

        if (response.hasError === 0) {
          // 筛选shuChuDM不为0且不为空且不为null的文书
          this.documentList = (response.data || []).filter(
            (doc) =>
              doc.shuChuDM && doc.shuChuDM !== '0' && doc.shuChuDM !== 0 && doc.shuChuDM !== null
          )

          // 设置弹窗显示
          this.documentSelectDialog.visible = true
          this.documentSearchKeyword = ''
        } else {
          this.$message.error(response.errorMessage || '获取文书列表失败')
        }
      } catch (error) {
        console.error('获取文书列表失败', error)
        this.$message.error('获取文书列表失败')
      } finally {
        this.loading = false
      }
    },

    // 点击已书写记录
    handleWrittenRecordClick(record) {
      this.currentRecord = record
      this.setIframeUrl(record)
    },

    // 设置iframe URL
    setIframeUrl(record) {
      if (!record) {
        this.iframeUrl = ''
        return
      }

      // 构建编辑URL
      this.iframeUrl = this.getRecordEditUrl(record)
    },

    // 获取记录编辑URL
    getRecordEditUrl(record) {
      if (!record || !record.id) return ''

      const idIsNumber = typeof record.id === 'number'
      // 构建URL参数
      const params = {
        as_blid: record.bingliID || this.bingLiID,
        as_gsdm: record.geshiDM || record.geShiDM,
        as_zyid: this.patientInfo.zhuYuanID,
        as_yhid: this.yongHuID,
        as_wsid: idIsNumber ? record.id : 0, // 新文书ID为0
        as_wslx: record.wenshuLX || record.wenShuLX,
        as_sscx: record.shoushuCX || record.shoushuCX,
        as_tmpid: 't1',
        tmpid: Math.random()
      }

      // 只有当通知单ID有值时才添加as_tzdid参数
      if (this.tongZhiDanID && this.tongZhiDanID !== '') {
        params.as_tzdid = this.tongZhiDanID
      }

      // 将参数转换为URL查询字符串
      const queryString = Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join('&')

      // 返回完整URL
      return `${this.baseUrl}/zyblws/blwsdetail.aspx?${queryString}`
    },

    // iframe加载完成
    handleIframeLoad() {
      // iframe加载完成后的处理
      console.log('iframe loaded')
    },

    // 保存记录 TODO：保存成功后判断当前手术次序第一条是否有geshiDM、wenshuLX、wenShuMC等数据
    async handleSaveClick() {
      if (!this.currentRecord) {
        this.$message.error('没有可保存的记录')
        return
      }

      // 获取iframe元素
      const iframe = this.$refs.contentIframe
      if (!iframe) {
        this.$message.error('iframe未加载完成')
        return
      }

      try {
        // 使用简化的iframe通信
        const message = await iframeCommunication.save(this.currentRecord.id || 'default', iframe)
        this.$message.success(message)

        // 保存成功后延迟刷新页面
        setTimeout(async () => {
          await this.fetchInitData()
        }, 1000)
      } catch (error) {
        console.error('保存失败', error)
        this.$message.error('保存失败: ' + (error.message || '未知错误'))
      }
    },

    // 删除记录
    async handleDeleteClick() {
      if (!this.currentRecord) {
        this.$message.error('没有可删除的记录')
        return
      }

      try {
        await this.$confirm('确定要删除这条记录吗？删除后不可恢复。', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 获取iframe元素
        const iframe = this.$refs.contentIframe
        if (!iframe) {
          this.$message.error('iframe未加载完成')
          return
        }

        // 使用简化的iframe通信
        const message = await iframeCommunication.delete(this.currentRecord.id || 'default', iframe)
        this.$message.success(message)

        // 删除成功后清空当前记录并重新获取列表
        this.currentRecord = null
        this.iframeUrl = ''
        setTimeout(async () => {
          await this.fetchInitData()
        }, 1000)
      } catch (error) {
        if (error.message && error.message !== '取消') {
          console.error('删除失败', error)
          this.$message.error('删除失败: ' + (error.message || '未知错误'))
        }
      }
    },

    // 打印记录
    async handlePrintClick() {
      if (!this.currentRecord) {
        this.$message.error('没有可打印的记录')
        return
      }

      // 获取iframe元素
      const iframe = this.$refs.contentIframe
      if (!iframe) {
        this.$message.error('iframe未加载完成')
        return
      }

      try {
        // 使用简化的iframe通信
        const message = await iframeCommunication.print(this.currentRecord.id || 'default', iframe)
        this.$message.success(message)
      } catch (error) {
        console.error('打印失败', error)
        this.$message.error('打印失败: ' + (error.message || '未知错误'))
      }
    },

    // 点击文书列表项
    async handleDocumentItemClick(doc) {
      try {
        if (!doc || !this.currentWritableRecord) {
          this.$message.warning('文书信息不完整')
          return
        }

        // 创建前判断是否需要绑定手术通知单
        const bindCheckResponse = await checkTZDBind({
          bingLiID: this.bingLiID,
          geShiDM: doc.geShiDM,
          shouShuCX: this.selectedSurgeryOrder
        })

        if (bindCheckResponse.hasError === 0) {
          const { tongZhiDan } = bindCheckResponse.data

          if (tongZhiDan === 0) {
            // 需要用户选择通知单，等待用户完成选择
            const notificationSelected = await this.showNotificationSelectDialog()
            if (!notificationSelected) {
              // 用户取消了选择，不继续创建记录
              return
            }
          } else if (tongZhiDan === -1) {
            // 不需要绑定通知单
            this.tongZhiDanID = ''
          } else if (tongZhiDan > 0) {
            // 直接使用该通知单ID
            this.tongZhiDanID = tongZhiDan
          }
        }

        // 使用选中的文书创建新记录
        const newRecord = {
          ...this.currentWritableRecord,
          ...doc,
          wenShuMC: doc.mingChen, // 使用mingChen作为文书名称
          geShiDM: doc.geShiDM
        }

        this.createNewRecord(newRecord)
        this.documentSelectDialog.visible = false

        this.currentWritableRecord = null
        this.documentSearchKeyword = ''
      } catch (error) {
        console.error('检查通知单绑定失败', error)
        this.$message.error('检查通知单绑定失败')
      }
    },

    // 取消选择文书
    handleDocumentSelectCancel() {
      this.documentSelectDialog.visible = false
      this.currentWritableRecord = null
      this.documentSearchKeyword = ''
    },

    // 显示手术通知单选择弹窗
    async showNotificationSelectDialog() {
      return new Promise(async (resolve) => {
        try {
          this.loading = true
          // 保存resolve函数，供选择和取消时调用
          this.notificationSelectResolve = resolve

          // 调用接口获取手术通知单列表
          const response = await getShouShuTZDByBLID({
            bingLiID: this.bingLiID
          })

          if (response.hasError === 0) {
            this.notificationList = response.data || []
            this.notificationDialog.visible = true
            this.selectedNotification = null
          } else {
            this.$message.error(response.errorMessage || '获取手术通知单列表失败')
            resolve(false) // 获取列表失败，返回false
          }
        } catch (error) {
          console.error('获取手术通知单列表失败', error)
          this.$message.error('获取手术通知单列表失败')
          resolve(false) // 发生错误，返回false
        } finally {
          this.loading = false
        }
      })
    },

    // 选择手术通知单
    handleNotificationSelect(row) {
      const notification = row
      this.selectedNotification = notification
      this.tongZhiDanID = notification.tongZhiDanID || ''
      this.notificationDialog.visible = false

      // 调用resolve函数，表示用户已选择通知单
      if (this.notificationSelectResolve) {
        this.notificationSelectResolve(true)
        this.notificationSelectResolve = null
      }
    },

    // 取消选择手术通知单
    handleNotificationSelectCancel() {
      this.notificationDialog.visible = false
      this.selectedNotification = null

      // 调用resolve函数，表示用户取消了选择
      if (this.notificationSelectResolve) {
        this.notificationSelectResolve(false)
        this.notificationSelectResolve = null
      }
    }
  }
}
</script>

<style scoped lang="scss">
.operative-records-container {
  height: 100%;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  background-color: #f6f6f6;
  position: relative;
}

.operative-records-layout {
  display: flex;
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.left-panel {
  width: 380px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #eff3fb;
  border-radius: 4px 0 0 4px;
}

.left-header {
  padding: 8px 10px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #eff3fb;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;

    .title-area {
      flex-shrink: 0;

      span {
        font-weight: bold;
        display: inline-block;
        border-left: 4px solid #356ac5;
        padding-left: 8px;
        color: #303133;
        font-size: 14px;
      }
    }

    .surgery-order-area {
      display: flex;
      align-items: center;
      flex-shrink: 0;

      .el-button {
        padding: 8px;
        font-size: 12px;
      }
    }
  }
}

// 可书写记录区域
.writable-records-section {
  flex: 0 0 auto;
  border-bottom: 1px solid #dcdfe6;
  background-color: #eff3fb;

  .section-header {
    padding: 8px 10px;
    background-color: #eaf0f9;
    border-bottom: 1px solid #dcdfe6;

    .section-title {
      font-weight: bold;
      font-size: 13px;
      color: #303133;
      border-left: 3px solid #356ac5;
      padding-left: 6px;
    }
  }

  .writable-records-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 5px;

    .writable-record-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      margin-bottom: 2px;
      background-color: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: #ecf5ff;
        border-color: #356ac5;
        transform: translateX(2px);
      }

      .record-name {
        font-size: 13px;
        color: #303133;
        flex: 1;
      }

      i {
        color: #356ac5;
        font-size: 14px;
      }
    }

    .empty-state {
      text-align: center;
      padding: 20px;
      color: #909399;
      font-size: 13px;
    }
  }
}

// 已书写记录区域
.written-records-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .section-header {
    padding: 8px 10px;
    background-color: #eaf0f9;
    border-bottom: 1px solid #dcdfe6;

    .section-title {
      font-weight: bold;
      font-size: 13px;
      color: #303133;
      border-left: 3px solid #356ac5;
      padding-left: 6px;
    }
  }

  .written-records-list {
    flex: 1;
    overflow-y: auto;
    padding: 5px;

    ::v-deep .el-collapse {
      border: none;

      .el-collapse-item {
        margin-bottom: 5px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        overflow: hidden;

        .el-collapse-item__header {
          background-color: #f5f7fa;
          border-bottom: 1px solid #e4e7ed;
          padding: 0 12px;
          height: 40px;
          line-height: 40px;
          font-size: 13px;
          font-weight: bold;
          color: #303133;

          &:hover {
            background-color: #ecf5ff;
          }

          .el-collapse-item__arrow {
            color: #356ac5;
          }
        }

        .el-collapse-item__wrap {
          border: none;

          .el-collapse-item__content {
            padding: 0;
          }
        }
      }
    }

    .collapse-content {
      background-color: #fff;

      .written-record-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background-color: #f5f7fa;
        }

        &.active {
          background-color: #ecf5ff;
          border-left: 3px solid #356ac5;
        }

        &:last-child {
          border-bottom: none;
        }

        .record-info {
          flex: 1;

          .record-name {
            font-size: 13px;
            color: #303133;
            margin-bottom: 2px;
          }

          .record-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }

      .empty-group {
        text-align: center;
        padding: 20px;
        color: #909399;
        font-size: 12px;
        background-color: #fafafa;
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #909399;
      font-size: 13px;
    }
  }
}

.right-panel {
  flex: 1;
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  background-color: #eff3fb;
  padding: 10px;
  border-radius: 4px;
}

.right-header {
  padding: 5px 20px;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #eaf0f9;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title {
    font-size: 14px;
    font-weight: bold;
    color: #303133;
    display: inline-block;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
  }

  .actions {
    display: flex;

    .el-button {
      padding: 5px 12px;
      font-size: 12px;
    }
  }
}

.content {
  flex: 1;
  position: relative;
  overflow: hidden;
  margin-top: 10px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #dcdfe6;

  iframe {
    width: 100%;
    height: 100%;
    border: none;
    display: block;
    border-radius: 4px;
  }

  .no-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #909399;
    font-size: 14px;
    background-color: #fafafa;

    i {
      font-size: 48px;
      margin-bottom: 16px;
      color: #c0c4cc;
    }

    p {
      margin: 0;
      font-size: 16px;
    }
  }
}

// 文书选择弹窗样式
.document-select-content {
  .search-area {
    margin-bottom: 15px;
  }

  .document-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 4px;

    .document-item {
      display: flex;
      height: 40px;
      padding: 0 12px;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;

      &:hover {
        background-color: #ecf5ff;
        border-left: 3px solid #356ac5;
      }

      &:last-child {
        border-bottom: none;
      }

      .document-info {
        flex: 1;

        .document-name {
          font-size: 14px;
          color: #303133;
          font-weight: 500;
        }

        .document-code {
          font-size: 12px;
          color: #909399;
        }
      }

      .document-action {
        i {
          color: #356ac5;
          font-size: 14px;
          transition: transform 0.3s;
        }
      }

      &:hover .document-action i {
        transform: translateX(2px);
      }
    }

    .empty-document {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #909399;

      i {
        font-size: 48px;
        margin-bottom: 12px;
        color: #c0c4cc;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}

::v-deep .el-dialog {
  .el-dialog__header {
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;

    .el-dialog__title {
      font-weight: bold;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    border-top: 1px solid #e4e7ed;
    background-color: #fafafa;
  }
}

// 手术通知单选择弹窗样式
.notification-select-content {
  .warning-notice {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 16px;
    background-color: #fef0f0;
    border: 1px solid #fbc4c4;
    border-radius: 4px;
    color: #f56c6c;
    font-size: 14px;
    font-weight: 500;

    i {
      font-size: 16px;
      margin-right: 8px;
    }

    span {
      flex: 1;
    }
  }

  ::v-deep .el-table {
    .el-table__header-wrapper {
      th {
        background-color: #f5f7fa;
        color: #303133;
        font-weight: 600;
      }
    }

    .el-table__body-wrapper {
      .el-table__row {
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background-color: #ecf5ff;
        }

        &.current-row {
          background-color: #ecf5ff;
        }
      }
    }

    .el-table__empty-block {
      padding: 40px 0;
      color: #909399;
    }
  }
}
</style>
