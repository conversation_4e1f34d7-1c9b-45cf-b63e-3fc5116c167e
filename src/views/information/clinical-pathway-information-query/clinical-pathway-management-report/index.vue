<template>
  <div class="container">
    <div class="header no-print">
      <div>
        <div class="header-item">
          <div class="header-item-title">选择日期:</div>
          <div class="header-item-date">
            <el-date-picker
              v-model="dateRange"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              size="default"
            />
          </div>
          <div class="header-item-title">选择专科:</div>
          <div class="header-item-radio">
            <el-select v-model="zhuanKeID" placeholder="请选择专科" autofocus filterable>
              <el-option
                v-for="item in zhuanKeList"
                :key="item.buMenID"
                :label="item.buMenMC"
                :value="item.buMenID.toString()"
              />
            </el-select>
          </div>
          <div class="header-item-title">选择统计类别:</div>
          <div class="header-item-radio">
            <el-radio-group v-model="tongJiLB">
              <el-radio label="0" size="large">
                <span class="search-label">按病种</span>
              </el-radio>
              <el-radio label="1" size="large">
                <span class="search-label">按治疗组</span>
              </el-radio>
            </el-radio-group>
          </div>
          <div class="header-item-button">
            <el-button @click="loadData">查询</el-button>
          </div>
          <div class="header-item-button">
            <el-button style="background-color: #356ac5" @click="exportToExcel">查询</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header no-print">
        <div class="title">临床路径管理报表</div>
      </div>
      <div class="table">
        <el-table max-height="648" stripe border :data="listsData">
          <el-table-column
            v-for="obj in tableData"
            :key="obj.value"
            :prop="obj.value"
            :width="obj.width"
            :label="obj.label"
            :align="obj.align"
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { initLcljGuanLiBB, getLcljGuanLiBB } from '@/api/clinical-pathway-performance-report'
import { format } from 'date-fns'
import { mapState } from 'vuex'
import * as XLSX from 'xlsx'
export default {
  data() {
    return {
      zhuanKeID: '',
      dateRange: [],
      tongJiLB: '0',
      //专科
      zhuanKeList: [],
      listsData: [],
      tableData: [
        {
          label: '科室',
          value: 'zhuanKeMC',
          width: '120',
          align: 'left'
        },
        {
          label: '病种',
          value: 'mingCheng',
          width: '300',
          align: 'left'
        },
        {
          label: '临床路径入径例数',
          value: 'ruJingShu',
          width: '150',
          align: 'left'
        },
        {
          label: '变异率%',
          value: 'bianYiLv',
          width: '100',
          align: 'right'
        },
        {
          label: '完成率%',
          value: 'wanChengLv',
          width: '100',
          align: 'right'
        },
        {
          label: '平均费用',
          value: 'pingJunFY',
          width: '100',
          align: 'right'
        },
        {
          label: '平均住院日',
          value: 'pingJunZYR',
          width: '100',
          align: 'right'
        },
        {
          label: '出院人数',
          value: 'chuYuanRS',
          width: '100',
          align: 'left'
        },
        {
          label: '管理率%',
          value: 'ruJingLv',
          width: '100',
          align: 'right'
        },
        {
          label: '诊断符合人次',
          value: 'zongShu',
          width: '100',
          align: 'left'
        },
        {
          label: '完成例数',
          value: 'wanChengCJS',
          width: '100',
          align: 'left'
        },
        {
          label: '变异例数', //字段缺少
          value: 'bianYiLv',
          width: '100',
          align: 'left'
        },
        {
          label: '入组率%',
          value: 'ruZuLv',
          width: '100',
          align: 'right'
        }
      ]
    }
  },
  computed: {
    ...mapState({
      curZhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    })
  },
  async mounted() {
    this.zhuanKeID = this.curZhuanKeID
    const start = format(new Date(), 'yyyy-MM')
    const end = format(new Date(), 'yyyy-MM')
    this.dateRange = [start, end]
    await this.initLcljData()
  },
  methods: {
    //查询
    async loadData() {
      const start = format(this.dateRange[0], 'yyyy-MM')
      const end = format(this.dateRange[1], 'yyyy-MM')
      const res = await getLcljGuanLiBB({
        kaiShiSJ: start,
        jieShuSJ: end,
        zhuanKeID: this.zhuanKeID,
        tongJiLB: this.tongJiLB
      })
      if (res.hasError === 0) {
        this.listsData =
          res.data.map((item) => {
            if (item) {
              item.bianYiLv = item.bianYiLv.toFixed(2)
              item.wanChengLv = item.wanChengLv.toFixed(2)
              item.ruJingLv = item.ruJingLv.toFixed(2)
              item.ruZuLv = item.ruZuLv.toFixed(2)
              item.pingJunZYR = item.pingJunZYR.toFixed(2)
            }
            return item
          }) || []
      }
    },
    //初始化
    async initLcljData() {
      const res = await initLcljGuanLiBB()
      if (res.hasError == 0) {
        this.zhuanKeList = res.data.zhuanKeList
      }
    },
    exportToExcel() {
      if (this.listsData.length == 0) {
        return this.$message.error('暂无数据可导出')
      }
      const arr = this.listsData.map((item) => {
        let d = {}
        this.tableData.forEach((col) => {
          d[col.label] = item[col.value]
        })
        return d
      })
      const worksheet = XLSX.utils.json_to_sheet(arr)
      // return
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      try {
        XLSX.writeFile(workbook, '临床路径管理报表.xlsx')
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  padding-bottom: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .input_txt {
    width: 235px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  .header-item-date {
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
  .header-item-radio {
    margin-right: 8px;
  }
  .item_date {
    width: 100px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.table {
  width: 1575px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
