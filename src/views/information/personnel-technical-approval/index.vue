<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <div class="header-item-title">提交日期:</div>
        <div class="header-item-date">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            size="default"
          />
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">科室:</div>
        <div class="header-item-input">
          <el-select v-model="zhuanKeID" placeholder="请选择专科" autofocus filterable>
            <el-option
              v-for="item in zhuanKeList"
              :key="item.daiMa"
              :label="item.mingCheng"
              :value="item.daiMa"
            />
          </el-select>
        </div>
        <div class="header-item-button"><el-button @click="onSerchData">查询</el-button></div>
        <div class="header-item-button">
          <el-button>进入维护页</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">（医务处）医疗人员医疗技术资格审批</div>
      </div>
      <div class="table">
        <el-table max-height="648" stripe border :data="listsData">
          <el-table-column prop="baoGaoid" width="100" label="报告ID"></el-table-column>
          <el-table-column prop="yongHuID" width="100" label="用户ID"></el-table-column>
          <el-table-column prop="wardBedNumber" width="100" label="姓名"></el-table-column>
          <el-table-column
            prop="hospitalizationNumber"
            width="100"
            label="终生码"
          ></el-table-column>
          <el-table-column prop="outpatientNumber" width="120" label="科室"></el-table-column>
          <el-table-column prop="applicationTime" width="150" label="提交日期"></el-table-column>
          <el-table-column
            prop="specialistApplication"
            width="100"
            label="当前状态"
          ></el-table-column>
          <el-table-column fixed="right" width="200" label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">查看</el-button>
              <el-button type="text" size="small" @click="handleClick(scope.row)">审批</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { getYiWuChuShenPiLieBiao } from '@/api/information'
import { getZhuanKeList } from '@/api/report-card'
import { format } from 'date-fns'
export default {
  data() {
    return {
      radio1: '1',
      dateRange: '',
      zhuanKeID: '0',
      zhuanKeList: [],

      listsData: [],
      tableData: []
    }
  },
  async mounted() {
    await this.fetchInit()
  },
  methods: {
    handleClick(row) {},
    async fetchInit() {
      const res = await getZhuanKeList()
    },
    async onSerchData() {
      const start = format(this.dateRange[0], 'yyyy-MM-dd HH:mm:ss')
      const end = format(this.dateRange[1], 'yyyy-MM-dd HH:mm:ss')
      const res = await getYiWuChuShenPiLieBiao({
        jieShuSJ: end,
        kaiShiSJ: start,
        keShiID: this.zhuanKeID || 0
      })
      if (res.hasError === 0) {
        this.listsData = res.data
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  .header-item-date {
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.table {
  width: 980px;
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
