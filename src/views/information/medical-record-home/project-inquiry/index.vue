<template>
  <div class="container">
    <div class="header">
      <div class="header-search">
        <div class="query-word">请输入病案号：</div>
        <div>
          <el-input v-model="inTypeValue" placeholder="请填写"></el-input>
        </div>
        <div class="button">
          <el-button type="primary" @click="searchFun1">查询</el-button>
        </div>
      </div>
      <div class="header-search">
        <div class="query-word">所属专科：</div>
        <el-select v-model="ZhiLiaoZu" filterable>
          <el-option
            v-for="item in ZhiLiaoZuList"
            :key="item.daiMa"
            :label="item.mingCheng"
            :value="item.daiMa"
          ></el-option>
        </el-select>
        <div class="query-word">出院日期 (开始时间－结束时间)：</div>
        <el-date-picker
          v-model="zhuanKeSJ"
          type="daterange"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="changeDay"
        ></el-date-picker>
        <div class="button">
          <el-button type="primary" @click="searchFun2">查询</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">出院病人一览表</div>
      </div>
      <div class="table">
        <el-table max-height="688" border stripe :data="patientData" style="width: 100%">
          <el-table-column prop="chuangWeiHao" width="140" label="病案号"></el-table-column>
          <el-table-column prop="xingMing" width="138" label="姓名"></el-table-column>
          <el-table-column prop="chuShengRQ" width="150" label="专科"></el-table-column>
          <el-table-column prop="chuShengRQ" width="190" label="出院时间"></el-table-column>
          <el-table-column prop="chuShengRQ" width="110" label="特护"></el-table-column>
          <el-table-column prop="bingQuRYRQ" width="110" label="一护"></el-table-column>
          <el-table-column prop="bingQuCYRQ" width="110" label="二护"></el-table-column>
          <el-table-column prop="hospitalDays" width="110" label="三护"></el-table-column>
          <el-table-column prop="hospitalDays" width="190" label="呼吸机"></el-table-column>
          <el-table-column prop="hospitalDays" width="100" label="31日再入院"></el-table-column>
          <el-table-column prop="hospitalDays" width="110" label="非计划再入院"></el-table-column>
          <el-table-column prop="hospitalDays" width="100" label="压疮"></el-table-column>
          <el-table-column prop="hospitalDays" width="100" label="医院感染"></el-table-column>
          <el-table-column prop="hospitalDays" width="100" label="并发症"></el-table-column>
          <el-table-column prop="hospitalDays" width="100" label="跌倒坠床"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getBinRenXXByBingAnAndXinXi,
  getBinRenXXByBingAnAndShiJian,
  getZhuanKeList
} from '@/api/medical-record-api'
import { format, subMonths, differenceInDays, parseISO } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      inType: '1',
      inTypeValue: '',
      ZhiLiaoZu: '',
      ZhiLiaoZuList: [],
      zhuanKeSJ: [format(subMonths(new Date(), 1), 'yyyy-MM-dd'), format(new Date(), 'yyyy-MM-dd')], // 默认值
      kaiShiSJ: format(subMonths(new Date(), 1), 'yyyy-MM-dd'), //开始时间
      jieShuSJ: format(new Date(), 'yyyy-MM-dd'), //结束时间
      // kaiShiSJ: '2023-07-09', //开始时间
      // jieShuSJ: '2023-07-15', //结束时间
      patientData: []
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit
    })
  },
  async mounted() {
    this.getZhuanKeList()
  },
  methods: {
    // 住院医生站_查询_查询一个出院病人记录
    async searchFun1() {
      try {
        const res = await getBinRenXXByBingAnAndXinXi({
          type: this.inType,
          param: this.inTypeValue
        })
        if (res.hasError === 0) {
          this.patientData = res.data

          // this.delArr()
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 住院医生站_查询_根据时间和专科查病人记录
    async searchFun2() {
      try {
        const res = await getBinRenXXByBingAnAndShiJian({
          kaiShiSJ: this.kaiShiSJ + ' 00:00:00',
          jieShuSJ: this.jieShuSJ + ' 23:59:59',
          type: '7',
          zhuanKeID: this.ZhiLiaoZu
        })
        if (res.hasError === 0) {
          this.patientData = res.data

          // this.delArr()
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取专科治疗组列表
    async getZhuanKeList() {
      try {
        const res = await getZhuanKeList()
        if (res.hasError === 0) {
          this.ZhiLiaoZuList = res.data
          this.ZhiLiaoZuList.unshift({
            daiMa: '0',
            mingCheng: '所有专科'
          })
          this.ZhiLiaoZu = this.ZhiLiaoZuList[0].daiMa
        }
      } catch (error) {
        console.log(error)
      }
    },

    delArr() {
      this.patientData = this.patientData.map((item) => {
        const admissionDate = parseISO(item.bingQuRYRQ)
        const dischargeDate = parseISO(item.bingQuCYRQ)
        const hospitalDays = differenceInDays(dischargeDate, admissionDate) + 1
        return {
          ...item,
          chuShengRQ: format(new Date(item.chuShengRQ), 'yyyy-MM-dd'),
          bingQuRYRQ: format(new Date(item.bingQuRYRQ), 'yyyy-MM-dd'),
          bingQuCYRQ: format(new Date(item.bingQuCYRQ), 'yyyy-MM-dd'),
          hospitalDays
        }
      })
    },

    changeDay(e) {
      this.kaiShiSJ = format(new Date(e[0]), 'yyyy-MM-dd')
      this.jieShuSJ = format(new Date(e[1]), 'yyyy-MM-dd')
    },

    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  // display: flex;
  // align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .header-search {
    display: flex;
    align-items: center;
    padding: 12px 14px;
    .query-word {
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 10px;
      margin-right: 10px;
    }
    .query-word:first-child {
      margin-left: 0px;
    }
    ::v-deep .el-button {
      background-color: #a66dd4;
      border: 1px solid #a66dd4;
      color: #fff;
    }
    .button {
      margin-left: 6px;
    }
  }
}
.content {
  background-color: #eaf0f9;
  height: 740px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}

::v-deep .el-table__cell {
  padding-left: 8px;
}

::v-deep .el-table__cell:last-child {
  padding-left: 0px;
}

::v-deep .el-table th.el-table__cell,
.el-table td.el-table__cell {
  padding-top: 9px;
  padding-bottom: 9px;
}

::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
  padding-top: 4px;
  padding-bottom: 4px;
}
</style>
