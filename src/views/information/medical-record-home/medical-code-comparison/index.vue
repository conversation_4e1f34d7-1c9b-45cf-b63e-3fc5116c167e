<template>
  <div class="container">
    <div class="header">
      <div class="query-word">病案代码对照:</div>
      <el-select v-model="medicalCode" @change="changeSelect">
        <el-option
          v-for="item in medicalCodeList"
          :key="item.daiMa"
          :label="item.mingCheng"
          :value="item.daiMa"
        ></el-option>
      </el-select>
      <!-- <div class="button">
        <el-button type="primary" @click="searchFun">查询</el-button>
      </div> -->
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">病案代码对照一览表</div>
      </div>
      <div class="table">
        <el-table max-height="688" border stripe :data="patientData" style="width: 798px">
          <el-table-column prop="daiMa" width="130" label="代码"></el-table-column>
          <el-table-column prop="mingCheng" width="180" label="代码名称"></el-table-column>
          <el-table-column prop="hisdm" width="250" label="HIS代码">
            <template #default="{ row }">
              <el-button type="primary" @click="searchFun">选择</el-button>
              <el-input v-model="row.hisdm" readonly class="select-input"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="hisdmmc" width="130" label="HIS代码名称"></el-table-column>
          <el-table-column prop="caoZuo" width="106" align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">保存</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getDuoZhiBiaoByDaiMaLB, getFuZaByDaiMaLB } from '@/api/medical-record-api'
import { format, subDays, differenceInDays, parseISO } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      shouShuMC: '',
      medicalCode: '3',
      medicalCodeList: [
        { daiMa: '3', mingCheng: '职业' },
        { daiMa: '6', mingCheng: '籍贯' },
        { daiMa: '7', mingCheng: '婚姻' },
        { daiMa: '8', mingCheng: '民族' },
        { daiMa: '9', mingCheng: '*职工*' }
      ],
      patientData: []
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit
    })
  },
  async mounted() {
    await this.getDuoZhiBiaoByDaiMaLB()
  },
  methods: {
    // 获取列表初始化接口
    async getDuoZhiBiaoByDaiMaLB() {
      try {
        const res = await getFuZaByDaiMaLB({
          lieBie: this.medicalCode
        })
        if (res.hasError === 0) {
          const filteredData = res.data.filter((item) => item.daiMa !== 0)
          this.patientData = filteredData.sort((a, b) => a.daiMa - b.daiMa)
        }
      } catch (error) {
        console.log(error)
      }
    },

    changeSelect() {
      this.getDuoZhiBiaoByDaiMaLB()
    },

    searchFun() {},

    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 740px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}

::v-deep .el-table__cell {
  padding-left: 8px;
}

::v-deep .el-table__cell:last-child {
  // padding-left: 0px;
}

::v-deep .el-table th.el-table__cell,
.el-table td.el-table__cell {
  padding-top: 9px;
  padding-bottom: 9px;
}

::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
  padding-top: 4px;
  padding-bottom: 4px;
}
.select-input {
  width: 160px;
  margin-left: 14px;
}
</style>
