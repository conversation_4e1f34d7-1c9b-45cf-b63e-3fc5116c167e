<template>
  <div class="side-container">
    <el-scrollbar>
      <div class="change-icon" @click="toggleSideBar">
        <i
          :style="{
            transform: !isCollapse ? '' : 'rotateY(180deg)',
            transition: 'transform 0.6s ease'
          }"
          class="iconfont icon-fanhui"
        />
      </div>
      <el-menu ref="menuRef" :collapse-transition="false" :collapse="isCollapse">
        <sidebar-item
          v-for="(route, index) in sidebarMenu"
          :key="index"
          :item="route"
          :base-path="route.path"
          :item-click-native-val="itemClickNativeVal"
          @itemClickNative="itemClickNative"
        />
      </el-menu>
    </el-scrollbar>
    <yu-chu-yuan-dialog
      :visible.sync="chuYuanVisible"
      :zhu-yuan-i-d-list="[patientInfo.zhuYuanID]"
    />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import SidebarItem from './components/SidebarItem'
import YuChuYuanDialog from '@/components/Dialog/yuChuYuanDialog.vue'
import { menuClickLog } from '@/api/log'
import store from '@/store'
import { EventBus } from '@/utils/event-bus'
import { isMyPatient, joinOrRemoveMyPatient } from '@/api/patient'

export default {
  name: 'Sidebar',
  components: { SidebarItem, YuChuYuanDialog },
  props: {
    isCollapse: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      itemClickNativeVal: '',
      sidebarMenu: [],
      chuYuanVisible: false,
      sidebarOpened: false
    }
  },

  computed: {
    ...mapGetters(['permission_routes', 'sidebar', 'sidebarRouters']),
    ...mapState({
      yongHuID: ({ user }) => user.yongHuID,
      token: ({ user }) => user.token,
      patientInfo: ({ patient }) => patient.patientInit
    }),
    patientDetailSiderBarRouter() {
      const indexRouter = this.sidebarRouters.filter((f) => f.path === '/patient-inside')
      return indexRouter[0].children
    },
    bingLiID() {
      return this.$route.params.id
    },
    linChuangLJ() {
      return this.patientInfo?.specialSign['临床路径']
    }
  },
  watch: {
    // activeMenu(val) {
    //   const title = val === this.$route.path ? this.$route.meta.title : ''
    //   const data = {
    //     appKey: getDefaultState().app_key,
    //     xiangXiMS: title,
    //     shuJuLX: 1,
    //     caoZuoLX: 4
    //   }
    //   addWebAuditLog(data)
    // }
  },
  mounted() {
    this.isMyPatient()
    EventBus.$on(`activeRoute_${this.bingLiID}`, (val) => {
      this.$refs.menuRef.activeIndex = val
    })
  },
  destroyed() {
    EventBus.$off(`activeRoute_${this.bingLiID}`)
  },
  methods: {
    toggleSideBar() {
      // store.dispatch('app/toggleSideBar')
      this.$emit('update:is-collapse', !this.isCollapse)
    },
    async itemClickNative(item) {
      console.log(item, this.patientDetailSiderBarRouter, '这里是itemClickNative')
      if (item.caiDanLX === 'F') {
        //按钮类型的操作
        switch (item.name) {
          case 'Join-mypatient': //加入我的病人
            joinOrRemoveMyPatient({
              bingLiID: this.bingLiID,
              leiBie: item.meta.title === '加入我的病人' ? 1 : 0
            }).then((res) => {
              if (res.hasError === 0) {
                this.$message.success('操作成功')
                this.isMyPatient()
              }
            })
            break
          case 'Join-day-surgery': //加入日间手术
            this.isJoinDaySurgery()
            break
          case 'supervisory-control': //院感监控
            window.open(
              `http://172.16.200.88/nis/cdc?userid=${this.yongHuID}&patientid=${this.patientInfo.zhuYuanHao}`
            )
            break
          case 'patient360': //患者360
            let url = `http://172.16.203.141:8080/winsso/c/${this.yongHuID}/${this.patientInfo.shenFenZH}/2/${this.patientInfo.bingRenBH}/${this.patientInfo.bingLiID}/0/0/${this.patientInfo.bingRenBH}/-1/47000592-2/0/63/zyysclient`
            window.open(url)
            break
          case 'MianDengLXX': //温州市区域医疗平台链接
            let url1 = `http://172.16.203.155:18087/webroot/decision/view/report?ref_c=d1dc9333-5ea0-4d18-908b-7d943cefd05d&viewlet=ysgzz%252Fqyzl_xzsj_no-limit.cpt&ref_t=design&bah=" + ${this.patientInfo.bingAnHao} + "&token=" + ${this.token}`
            window.open(url1)
            break
          case 'Discharge': //预出院
            this.chuYuanVisible = true
            break
          case 'Join-Clinical-Path':
            let compare = ''
            console.log(this.linChuangLJ)
            if (this.linChuangLJ === '0' || !this.linChuangLJ) {
              compare = '@/views/patient-inside/clinical-path/JoinClinicalPath.vue'
            } else if (this.linChuangLJ === '21') {
              compare = '@/views/patient-inside/clinical-path/ClinicalPath.vue'
            } else if (this.linChuangLJ === '29') {
              compare = '@/views/patient-inside/clinical-path/ClinicalPath.vue'
            } else {
              compare = '该病人是老版临床路径，请在电子病历3.0里操作'
              this.$message.error(compare)
              return
            }
            EventBus.$emit(`sidebarClick_${this.bingLiID}`, {
              name: 'ClinicalPath',
              component: () => import('@/views/patient-inside/clinical-path/ClinicalPath.vue'),
              meta: {
                title: '临床路径'
              }
            })
            break
        }
      } else {
        this.itemClickNativeVal = item.name
        EventBus.$emit(`sidebarClick_${this.bingLiID}`, item)
        await this.$store.dispatch('patient/setSideBarRoute', item.name)
      }
      // 记录菜单点击
      if (item?.meta?.caiDanID) {
        menuClickLog({
          caiDanID: item?.meta?.caiDanID
        })
      }
    },
    // 判断是否我的病人
    async isMyPatient() {
      await this.$store.dispatch('patient/getPatientInit', this.bingLiID)
      //判断是否加入临床路径
      this.isJoinClincalPath()
      this.sidebarMenu = [].concat(this.patientDetailSiderBarRouter)
      isMyPatient({ bingLiID: this.bingLiID, yongHuID: this.yongHuID }).then((res) => {
        if (res.hasError === 0) {
          this.changeMenuTitle(
            this.sidebarMenu,
            'Join-mypatient',
            res.data ? '移除我的病人' : '加入我的病人'
          )
        }
      })
    },
    // 判断是否加入日间手术
    isJoinDaySurgery() {
      let _this = this
      const arr = [].concat(this.patientDetailSiderBarRouter)
      for (let item of arr) {
        if (item.children && item.children.length > 0) {
          item.children.map(function (key, index) {
            if (key.name === 'Join-day-surgery') {
              item.children.splice(index, 1)
            }
          })
        }
      }
      this.sidebarMenu = arr
      return false
    },
    // 判断是否加入临床路径
    isJoinClincalPath() {
      this.sidebarMenu = [].concat(this.patientDetailSiderBarRouter)
      let title = '',
        name = ''
      if (
        this.patientInfo.specialSign &&
        (this.patientInfo.specialSign['临床路径'] === '0' ||
          !this.patientInfo.specialSign['临床路径'])
      ) {
        // name = 'Join-Clinical-Path'
        title = '加入临床路径'
      } else {
        // name = 'Clinical-Path'
        title = '临床路径'
      }
      this.changeMenuTitle(this.sidebarMenu, 'Join-Clinical-Path', title)
      return false
    },
    //更新菜单栏
    changeMenuTitle(menu, name, newTitle, type) {
      for (let item of menu) {
        if (item.name === name) {
          item.meta.title = newTitle
          return true
        }
        if (item.children && item.children.length > 0) {
          const found = this.changeMenuTitle(item.children, name, newTitle)
          if (found) return true
        }
      }
      return false
    },
    //删除菜单栏
    deleteMenuTitle(menu, name) {
      menu.map((item, index) => {
        if (item.name === name) {
          menu.splice(index, 1)
          return true
        }
        if (item.children && item.children.length > 0) {
          const found = this.changeMenuTitle(item.children, name)
          if (found) return true
        }
      })
      return false
    }
  }
}
</script>
<style lang="scss" scoped>
.side-container {
  .el-scrollbar {
    height: calc(100% + 7px) !important;
  }

  .change-icon {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 40px;
    margin-left: 25px;
    font-size: 14px;
    line-height: 100%;
    color: #fff;
    text-align: center;
    cursor: pointer;

    .icon_box {
      width: 14px;
      height: 14px;
      font-size: 14px;
    }
  }

  .el-scrollbar {
    height: calc(100% - 40px);
  }
}

.el-menu {
  margin-top: 10px;
}
</style>
