<template>
  <keep-alive :include="cachedViews" :max="8">
    <router-view :key="key" style="height: calc(100% - 40px); width: 100%" />
  </keep-alive>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    // 需要缓存的页面 固钉
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.fullPath
    }
  }
}
</script>

<style lang="scss" scoped></style>
