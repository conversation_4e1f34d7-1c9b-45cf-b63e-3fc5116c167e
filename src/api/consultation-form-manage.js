import request from '@/utils/request'
import { getYongHuID } from '@/utils/auth'

// 会诊单_查询_获取会诊单申请页面初始化数据

export function getAddConsulationInitData(param) {
  return request({
    url: '/app-emrservice/v1/consulation/getAddConsulationInitData',
    method: 'post',
    data: param,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

//  会诊单_查询_新增会诊单页获取病人信息
export function getConsulationData(params) {
  return request({
    url: '/medicalrecord/v1/Consulation/getConsulationData',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 会诊单_查询_新增会诊单页获取病人信息
export function getPatientofConsulation(params) {
  return request({
    url: '/app-emrservice/v1/consulation/getPatientofConsulation',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 会诊单_查询_新增会诊单页获取病人信息
export function getConsulationByBLID(params) {
  return request({
    url: '/app-emrservice/v1/consulation/getConsulationByBLID',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 会诊单_保存_新增或修改会诊单
export function newConsulation(params) {
  return request({
    url: '/app-emrservice/v1/consulation/newConsulation',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 会诊单查询获取会诊专科对应医生列表
export function getDoctorOfConsulation(params) {
  return request({
    url: '/app-emrservice/v1/consulation/getDoctorOfConsulation',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 会诊单_短信_发送会诊短信(申请)
export function sendConsulationMessageSP(params) {
  return request({
    url: '/app-emrservice/v1/consulation/sendConsulationMessageSP',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 会诊单_查询_按会诊标志查询会诊单列表
export function getConsulationListByHZBZ(params) {
  return request({
    url: '/medicalrecord/v1/Consulation/getConsulationListByHZBZ',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 初始化特殊抗菌药物会诊单
export function initTskjywhzd(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/initTskjywhzd',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 初始化特殊抗菌药物会诊单列表
export function initTskjywhzdList(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/initTskjywhzdList',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 新增特殊抗菌药物会诊单
export function addTeShuKJYWHZD(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/addTeShuKJYWHZD',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 修改_【提交】特殊抗菌药物会诊单
export function tiJiaoTSKJYWHZD(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/tiJiaoTSKJYWHZD',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 修改_【取消提交】特殊抗菌药物会诊单
export function quXiaoTJTSKJYWHZD(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/quXiaoTJTSKJYWHZD',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 根据【病历ID】查询【特殊抗菌药物会诊单列表】
export function getTeShuKJYWHZDListByBlid(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/getTeShuKJYWHZDListByBlid',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 根据【会诊单ID】查询【特殊抗菌药物会诊单】
export function getTeShuKJYWHZD(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/getTeShuKJYWHZD',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 更新特殊抗菌药物会诊单
export function updateTeShuKJYWHZD(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/updateTeShuKJYWHZD',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 查询_获取微生物送检
export function getWeiShengWuSJ(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/getWeiShengWuSJ',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 查询_获取今日会诊专家
export function getJinRiHuiZhenZJ(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/getJinRiHuiZhenZJ',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 查询_获取辅助检查
export function getFuZhuJC(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/getFuZhuJC',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 查询_搜索抗菌药品
export function searchKangJunYP(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/searchKangJunYP',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 初始化三联抗菌药物会诊单
export function initSlkjywhzd(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/initSlkjywhzd',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 初始化三联抗菌药物会诊单列表
export function initSlkjywhzdList(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/initSlkjywhzdList',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 根据【病历ID】查询【三联抗菌药物会诊单列表】
export function getSanLianKJYWHZDListByBlid(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/getSanLianKJYWHZDListByBlid',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 根据【会诊单ID】查询【三联抗菌药物会诊单】
export function getSanLianKJYWHZD(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/getSanLianKJYWHZD',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 新增三联抗菌药物会诊单
export function addSanLianKJYWHZD(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/addSanLianKJYWHZD',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 初始化三联抗菌药物会诊单列表
export function updateSanLianKJYWHZD(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/updateSanLianKJYWHZD',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 修改_【提交】三联抗菌药物会诊单
export function tiJiaoSLKJYWHZD(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/tiJiaoSLKJYWHZD',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 修改_【取消提交】三联抗菌药物会诊单
export function quXiaoTJSLKJYWHZD(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/quXiaoTJSLKJYWHZD',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}
