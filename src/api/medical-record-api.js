import request from '@/utils/request'

// 住院医生站_查询_查询一个出院病人记录
export function getBinRenXXByBingAnAndXinXi(params) {
  return request({
    url: '/app-emrservice/v1/MedicalRecordInterface/getBinRenXXByBingAnAndXinXi',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_根据时间和专科查病人记录
export function getBinRenXXByBingAnAndShiJian(data) {
  return request({
    url: '/app-emrservice/v1/MedicalRecordInterface/getBinRenXXByBingAnAndShiJian',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 获取专科
export function getZhuanKeList(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getZhuanKeList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_根据类别查询专科编码员信息
export function getBianMaYuanAllByLieBie(params) {
  return request({
    url: '/app-emrservice/v1/MedicalRecordInterface/getBianMaYuanAllByLieBie',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_病案职工信息
export function getBingAnZhiGongXXByPage(params) {
  return request({
    url: '/app-emrservice/v1/MedicalRecordInterface/getBingAnZhiGongXXByPage',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询所有员工信息（YL_RYK）
export function getYlRykList(data) {
  return request({
    url: '/app-emrservice/v1/basicInfo/v1/doctor/getYlRykList',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_病案科室信息
export function getBingAnKeShiDMByPage(params) {
  return request({
    url: '/app-emrservice/v1/MedicalRecordInterface/getBingAnKeShiDMByPage',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 保存一条专科编码员信息
export function saveBianMaYuanXX(data) {
  return request({
    url: '/app-emrservice/v1/MedicalRecordInterface/saveBianMaYuanXX',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_根据病案类别查询多值表
export function getDuoZhiBiaoByDaiMaLB(params) {
  return request({
    url: '/app-emrservice/v1/MedicalRecordInterface/getDuoZhiBiaoByDaiMaLB',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_根据病案类别查询病案代码对照
export function getFuZaByDaiMaLB(params) {
  return request({
    url: '/app-emrservice/v1/MedicalRecordInterface/getFuZaByDaiMaLB',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
