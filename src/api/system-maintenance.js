import request from '@/utils/request'
import { getYongHuID } from '@/utils/auth'

//治疗医嘱

export function getZhiLiaoMbMlByLb(leiBie) {
  return request({
    url: '/medicaladvice/v1/treatment/getZhiLiaoMbMlByLb',
    method: 'get',
    params: {
      leiBie
    },
    headers: {
      verifyApp: false
    }
  })
}

export function getZhiLiaoMbZh(params) {
  return request({
    url: '/medicaladvice/v1/treatment/getZhiLiaoMbZh',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

export function saveZhiLiaoMbMl(params) {
  return request({
    url: '/medicaladvice/v1/treatment/saveZhiLiaoMbMl',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

export function saveZhiLiaoMbZh(params) {
  return request({
    url: '/medicaladvice/v1/treatment/saveZhiLiaoMbZh',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

export function zhiLiaoMbInit() {
  return request({
    url: '/medicaladvice/v1/treatment/zhiLiaoMbInit',
    method: 'post',
    headers: {
      verifyApp: false
    }
  })
}

//综合遗嘱

export function getZongHeYzMbMlByID(moBanID) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getZongHeYzMbMlByID',
    method: 'get',
    params: {
      moBanID
    },
    headers: {
      verifyApp: false
    }
  })
}

export function getZongHeYzMbMlByLb(moBanLB) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getZongHeYzMbMlByLb',
    method: 'get',
    params: {
      moBanLB
    },
    headers: {
      verifyApp: false
    }
  })
}

export function getZongHeYzMbMx(moBanID) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getZongHeYzMbMx',
    method: 'get',
    params: {
      moBanID
    },
    headers: {
      verifyApp: false
    }
  })
}

export function saveZongHeYzMbMl(params) {
  return request({
    // url: '/app-emrservice/v1/mdedicalAdviceZYYZ/saveZongHeYzMbMl',
    url: '/medicaladvice/v1/AdviceInpatient/saveZongHeYzMbMl',
    method: 'post',
    data: params,
    headers: {
      accept: '*/*',
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

export function saveZongHeYzMbMx(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/saveZongHeYzMbMx',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

export function zongHeYzMbInit() {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/zongHeYzMbInit',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

//有创模板

//查询本有创记录模板关联的专科
export function getMbglData(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getMbglData',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//获取所有有创纪录模板
export function getYcjlData() {
  return request({
    url: '/app-emrservice/v1/basicInfo/getYcjlData',
    method: 'post',
    headers: {
      verifyApp: false
    }
  })
}

//根据拼音或关键字获取有创纪录模板
export function getYcjlDataByPYGJZ(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getYcjlDataByPYGJZ',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//保存_新增或修改有创记录
export function saveYcjl(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/saveYcjl',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

//删除_删除多条有创模板关联专科记录
export function deleteBatchMbgl(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/deleteBatchMbgl',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

//医疗证明管理

export function medicalCertificateInit() {
  return request({
    url: '/app-emrservice/v1/yiLiaoZM/medicalCertificateInit',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}
// 获取出院病人病理结果提醒列表
export function getInPatientCheckResultMes(params) {
  return request({
    url: '/medicalrecord/v1/PatientAttributeOperation/getInPatientCheckResultMes',
    method: 'get',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_分页查询YlBasySswy
export function getYlBasySswyByPage(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getYlBasySswyByPage',
    method: 'get',
    params
  })
}

// 住院医生站_保存_保存一条YlBasySswy内容
export function saveYlBasySswy(data) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/saveYlBasySswy',
    method: 'post',
    data
  })
}

// 获取手术项目列表
export function getShouShuLieBiao(params) {
  return request({
    url: '/app-emrservice/v1/YlSsicd/getShouShuLieBiao',
    method: 'get',
    params
  })
}

// 住院医生站_保存_保存一条YlSsicd内容
export function saveYlSsicd(data) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/saveYlSsicd',
    method: 'post',
    data
  })
}

// 住院医生站_查询_分页查询手术icd
export function getYlSsicdByPage(params) {
  return request({
    url: 'app-emrservice/v1/MedicalrecordDocument/getYlSsicdByPage',
    method: 'get',
    params
  })
}

// 输血审批接口
export function approval(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/approval',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 全部输血审批查询接口
export function getApprovalAll(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/getApprovalAll',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 科室审批查询接口(当日800ml-1600ml)
export function getApprovalFromKeShi(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/getApprovalFromKeShi',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 上级医师审批查询接口（当日800ml以内）
export function getApprovalFromShangJiYiShi(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/getApprovalFromShangJiYiShi',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 医务科审批查询接口(当日1600ml以上)
export function getApprovalFromYiWuKe(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/getApprovalFromYiWuKe',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 无需审批查询接口
export function getApprovalWithoutSP(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/getApprovalWithoutSP',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 输血单页面基础数据
export function getBaseInfo(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/getBaseInfo',
    method: 'get',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_病历讨论记录查询
export function getBingLiTaoLunJiLu(data) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getBingLiTaoLunJiLu',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_危重病人记录查询
export function getWeiZhongSiWangBRCX(data) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getWeiZhongSiWangBRCX',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_病历状态修改记录查询_根据专科
export function getBingLiZTXGJLByZhuanKe(params) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getBingLiZTXGJLByZhuanKe',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_病历状态修改记录查询_根据病案号
export function getBingLiZTXGJLByBingAnHao(params) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getBingLiZTXGJLByBingAnHao',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 医嘱按时间间隔统计
export function getTongJiYzBySjjg(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getTongJiYzBySjjg',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_专科病历上缴率查询
export function getBingLiShangJiaoLvTJB(data) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getBingLiShangJiaoLvTJB',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_病区病历上缴率查询
export function getBingLiShangJiaoLvTJBBQ(data) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getBingLiShangJiaoLvTJBBQ',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_监管医师监管病历统计表
export function getJianGuanYSJGBLTJBCX(data) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getJianGuanYSJGBLTJBCX',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
