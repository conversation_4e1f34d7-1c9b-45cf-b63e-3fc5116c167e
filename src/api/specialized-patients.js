import request from '@/utils/request'

// 住院医生站_根据出院时间专科ID查询_返回专科出院病人列表
export function get15TNCYBRByTime(params) {
  return request({
    url: '/app-emrservice/v1/ezyblbr/get15TNCYBRByTime',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_根据出院时间专科ID和治疗组ID查询_返回专科出院病人列表
export function get15TNCYBRByZLZByTime(params) {
  return request({
    url: '/app-emrservice/v1/ezyblbr/get15TNCYBRByZLZByTime',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_本专科转科病人列表
export function getEZyblbrTransferData(params) {
  return request({
    url: '/patient/v1/ezyblbr/getEZyblbrTransferData',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 本专科转科病人列表_归档
export function guiDang(params) {
  return request({
    url: '/patient/v1/ezyblbr/guiDang',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科id获取治疗组列表
export function getZhiLiaoZuListByZhuanKeID(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getZhiLiaoZuListByZhuanKeID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 本专科在院病人列表
export function admission(params) {
  return request({
    url: '/app-emrservice/v1/admission/getZhuanKeZYLB',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 时间针提醒
export function getShiJianZhenTX(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getShiJianZhenTX',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取时间针医嘱
export function getShiJianZhenYz(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getShiJianZhenYz',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 删除时间针提醒
export function deleteShiJianZhenTx(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/deleteShiJianZhenTx',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 获取电子病历用户可以操作的专科列表
export function getZhuanKeList() {
  return request({
    url: `/sysmanage/v1/user/getClinicDeptListForEHRUser`,
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

// 跨科治疗_根据病例ID获取数据
export function getListByBingLiID(id) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/getListByBingLiID?bingLiID=' + id,
    method: 'get',
    id,
    headers: {
      verifyApp: false
    }
  })
}

// 跨科治疗病人列表_根据条件获取数据
export function getListByCondition(params) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/getListByCondition',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 转/跨科病历修正申请单_根据ID取消
export function cancelBlxztzd(params) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/cancelBlxztzd',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 转/跨科病历修正申请单_根据ID确认
export function confirmBlxztzd(params) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/confirmBlxztzd',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 转/跨科病历修正申请单_根据病例ID获取最新转床时间

export function getBrZksjByBlid(params) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/getBrZksjByBlid',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 转/跨科病历修正申请单_根据通知用户ID获取数据
export function getListByTongZhiYongHuID(params) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/getListByTongZhiYongHuID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 转/跨科病历修正申请单_新增病历修正申请单
export function insertSingleBlxzsqd(data) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/insertSingleBlxzsqd',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 转/跨科病历修正申请单_根据条件搜索
export function searchBlxztzdByCondition(data) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/searchBlxztzdByCondition',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 转/跨科病历修正申请单_修正
export function updateBlxztzdStatus(params) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/updateBlxztzdStatus',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 转/跨科病历修正申请单_更新
export function updateSingleBlxzsqd(data) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/updateSingleBlxzsqd',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据人员库ID获取治疗组列表
export function getZhiLiaoZuListByRYKID(data) {
  return request({
    url: '/medicalrecord/v1/BasicData/getZhiLiaoZuListByRYKID',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据治疗组获取住院患者信息
export function getInPatientListByZLZID(data) {
  return request({
    url: '/patient/v1/in-patient/getInPatientListByZLZID',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据病历id列表获取未打印化验单信息
export function getLPatientinfoByBingLiIdList(data) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getLPatientinfoByBingLiIdList',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
