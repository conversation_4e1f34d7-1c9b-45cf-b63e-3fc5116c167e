import request from '@/utils/request'
// 医务处指定跨科病人
/**
 * 根据病案号获取病历ID和患者状态
 * @param {Object} params 参数对象
 * @param {string} params.empi 病案号
 * @returns {Promise}
 */
export function getEzyblbrByEmpi(params) {
  return request({
    url: '/app-emrservice/v1/ezyblbr/getEzyblbrByEmpi',
    method: 'get',
    params
  })
}

/**
 * 根据条件搜索跨科病人列表
 * @param {Object} params 参数对象
 * @param {string} params.kaiShiSJ 开始时间
 * @param {string} params.jieShuSJ 结束时间
 * @param {string} params.yuWoYG 与我有关
 * @returns {Promise}
 */
export function getListByCondition2(params) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/getListByCondition2',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 新增跨科治疗记录
 * @param {Object} data 记录数据
 * @returns {Promise}
 */
export function insertKuaKeZLJL(data) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/insertKuaKeZLJL',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 获取所有部门列表
 * @returns {Promise}
 */
export function getAllBuMen() {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/getAllBuMen',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 启用跨科治疗记录
 * @param {Object} params 参数对象
 * @param {string} params.ID 记录ID
 * @returns {Promise}
 */
export function startKkzljl(params) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/startKkzljl',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 停用跨科治疗记录
 * @param {Object} params 参数对象
 * @param {string} params.ID 记录ID
 * @returns {Promise}
 */
export function stopKkzljl(params) {
  return request({
    url: '/medicaladvice/v1/InterdisciplinaryTreatment/stopKkzljl',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院超30天患者管理
/**
 * 初始化
 * @returns {Promise}
 */
export function getOver30DaysStayZKList(params) {
  return request({
    url: '/app-emrservice/v1/admission/getOver30DaysStayZKList',
    method: 'get',
    params
  })
}

/**
 * 获取住院超30天报表列表
 * @param {Object} params 参数对象
 * @param {string} params.jieShuSJ 结束时间
 * @param {string} params.kaiShiSJ 开始时间
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function getOver30DaysStayList(params) {
  return request({
    url: '/app-emrservice/v1/admission/getOver30DaysStayList',
    method: 'get',
    params
  })
}
//住院医生站_病历查询_病历上缴情况查询
export function getBingLiShangJiaoQingKuangCX(data) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getBingLiShangJiaoQingKuangCX',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

//药占比报表
export function getYaoZhanBiBB(data) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/getYaoZhanBiBB',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

//获取专科，sjbm=1，ztbz=1
export function getZhuanKeList() {
  return request({
    url: '/app-emrservice/v1/basicInfo/getZhuanKeList',
    method: 'get',
    params: {
      sjbm: 1,
      ztbz: 1
    }
  })
}
// 转日间审批
/**
 * 查询病人列表
 * @param {Object} params 参数对象
 * @param {string} params.bingAnHao 病案号
 * @param {string} params.jieShuSJ 结束时间
 * @param {string} params.kaiShiSJ 开始时间
 * @param {string} params.leiBie 类别 1- 普通转日间 2-日间转普通
 * @param {string} params.shiFouZY 是否住院
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function daySurgeryPatInfo(params) {
  return request({
    url: '/medicaladvice/v1/DaySurgery/getPatInfo',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
//医嘱点评_筛查接口(点击筛查时调用)
export function getBrList(data) {
  return request({
    url: '/app-emrservice/v1/OrderReview/getBrList',
    method: 'post',
    data
  })
}
/**
 * 审批
 * @param {Object} params 参数对象
 * @param {string} params.ID ID
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.shenPiYJ 审批意见
 * @param {string} params.shenPiZT 审批状态
 * @param {string} params.zhuYuanID 住院ID
 * @returns {Promise}
 */
export function daySurgeryShenPi(params) {
  return request({
    url: '/medicaladvice/v1/DaySurgery/shenPi',
    method: 'post',
    params
  })
}

// 【手术通知单查询】 获取手术通知单列表
export function getSurgeryNoticeList(params) {
  return request({
    url: 'medicaladvice/v1/operationInpatient/getSurgeryNoticeList',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}
//获取病区，sjbm=1，ztbz=1
export function getBingQuList() {
  return request({
    url: '/app-emrservice/v1/basicInfo/getBingQuList',
    method: 'get',
    params: {
      sjbm: 1,
      ztbz: 1
    },
    headers: {
      verifyApp: false
    }
  })
}

// 营养评估单查询
/**
 * 获取营养评估单列表
 * @param {Object} params 参数对象
 * @param {string} params.jieShuSJ 结束时间
 * @param {string} params.kaiShiSJ 开始时间
 * @param {string} params.pingGuDanLX 评估单类型 rbSGA-SGA评估单 rbPGSGA-PG-SGA评估单 rbPGD-住院病人营养访视单 rbGLIM-GLIM营养评估单 rbMNASF-MNASF营养评估单
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function getNutritionRecordList(params) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/getNutritionRecordList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 【日间手术查询】 获取日间手术列表
export function getDaySurgeryList(params) {
  return request({
    url: 'medicaladvice/v1/DaySurgery/getDaySurgeryList',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

// 【医务处多学科会诊审批】 获取会诊单审批列表
export function getConsulationDXKSPList(params) {
  return request({
    url: 'medicalrecord/v1/Consulation/getConsulationDXKSPList',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

// 【重大手术二次审批】 初始化
export function erCiSPInit() {
  return request({
    url: 'medicaladvice/v1/operationInpatient/erCiSPInit',
    method: 'post',
    headers: {
      verifyApp: false
    }
  })
}

// 【重大手术二次审批】 获取二次审批列表
export function getErCiSPList(params) {
  return request({
    url: 'medicaladvice/v1/operationInpatient/getErCiSPList',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 营养风险筛查点评统计信息查询
/**
 * 获取营养评估单列表
 * @param {Object} params 参数对象
 * @param {string} params.jieShuSJ 结束时间
 * @param {string} params.kaiShiSJ 开始时间
 * @param {string} params.bingQuID 病区ID
 * @param {string} params.zhiLiaoZuID 治疗组ID
 * @param {string} params.selectType 评估单类型 rbSGA-SGA评估单 rbPGSGA-PG-SGA评估单 rbPGD-住院病人营养访视单 rbGLIM-GLIM营养评估单 rbMNASF-MNASF营养评估单
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function getYlYyscdpList(params) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/getYlYyscdpList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取病区营养筛查点评正确率列表
/**
 * 获取病区营养筛查点评正确率统计数据
 * @param {Object} params 参数对象
 * @param {string} params.jieShuSJ 结束时间
 * @param {string} params.kaiShiSJ 开始时间
 * @returns {Promise}
 */
export function getCommentStatisticInAllWards(params) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/getCommentStatisticInAllWards',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 入院24小时内营养风险筛查病人查询
/**
 * 入院24小时内营养筛查未做营养师点评列表
 * @param {Object} params 参数对象
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function getUnCommentListByYYS(params) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/getUnCommentListByYYS',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 入院24小时内营养筛查未做护士长交叉点评列表
 * @param {Object} params 参数对象
 * @param {string} params.bingQuID 病区ID
 * @returns {Promise}
 */
export function getUnCommentListByJCDP(params) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/getUnCommentListByJCDP',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 入院24小时内营养筛查未做护士长点评列表
 * @param {Object} params 参数对象
 * @param {string} params.bingQuID 病区ID
 * @returns {Promise}
 */
export function getUnCommentListByHSZ(params) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/getUnCommentListByHSZ',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 住院医生站_病历查询_归档报表查询
 * @param {Object} data 参数对象
 * @param {string} data.jieShuSJ 结束时间
 * @param {string} data.kaiShiSJ 开始时间
 * @param {string} data.zhuanKeID 专科ID
 * @param {string} data.leiBie 专科ID
 * @returns {Promise}
 */
export function getGuiDangBBCX(data) {
  return request({
    url: 'app-emrservice/v1/BingLiCX/getGuiDangBBCX',
    method: 'POST',
    data
  })
}
// 【重大手术二次审批】 保存二次审批
export function saveECSP(params) {
  return request({
    url: 'medicaladvice/v1/operationInpatient/saveECSP',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

//通用_查询_根据专科ID获取治疗组列表
export function getTherapyGroupsByZhuanKeID(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/v1/doctor/getTherapyGroupsByZhuanKeID',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//电子病历_查询_根据条件查询筛查记录列表
export function getScjlListByTimeType(params) {
  return request({
    url: '/medicalrecord/v1/OrderReview/getScjlListByTimeType',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 通用_查询_获取【医疗用户信息】
export function getMedicalStaffBaseinfo2(params) {
  return request({
    url: `/staff/v1/doctor/getMedicalStaffBaseinfo2`,
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 电子病历_删除_根据筛查ID删除筛查记录
export function deleteShaiChaJL(params) {
  return request({
    url: `/medicalrecord/v1/OrderReview/deleteShaiChaJL`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 单个病人筛查
export function getBrSingle(params) {
  return request({
    url: `/medicalrecord/v1/OrderReview/getBrSingle`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 电子病历_查询_根据筛查ID和病例ID获取筛查明细
export function getScmxByScidBlid(params) {
  return request({
    url: `/medicalrecord/v1/OrderReview/getScmxByScidBlid`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
