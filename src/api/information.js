import request from '@/utils/request'

// 安全模块_查询_获取所有病区
export function getAllWardList(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getAllWardList',
    method: 'POST',
    params
  })
}

// 根据病人基本信息查询患者
export function getBinRenXXByParam(params) {
  return request({
    url: '/app-emrservice/v1/ezyblbr/getBinRenXXByParam',
    method: 'GET',
    params,
    timeout: 60 * 1000
  })
}

// 根据病人出院时间和诊断查询病人信息
export function getBinRenXXByZD(params) {
  return request({
    url: '/app-emrservice/v1/ezyblbr/getBinRenXXByZD',
    method: 'GET',
    params,
    timeout: 60 * 1000
  })
}

// 根据病人出入院时间和病区查询病人信息
export function getBinRenXXByTime(params) {
  return request({
    url: '/app-emrservice/v1/ezyblbr/getBinRenXXByTime',
    method: 'GET',
    params,
    timeout: 60 * 1000
  })
}

// 住院医生站_病历查询_手机录入文本查询
export function getShouJiLR(params) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getShouJiLR',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_根据ID删除手机录入文本查询
export function deleteShouJiXXByID(params) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/deleteShouJiXXByID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询微生物无样可采理由(专科)
export function getWeiShengWuWykclyByZk(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getWeiShengWuWykclyByZk',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 查询微生物无样可采理由(病案号)
export function getWeiShengWuWykclyByBah(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getWeiShengWuWykclyByBah',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 住院医生站_查询_获取医疗人员资质医务处审批列表
export function getYiWuChuShenPiLieBiao(params) {
  return request({
    url: '/app-emrservice/v1/Medicalqualification/getYiWuChuShenPiLieBiao',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 病人病历状态修改_根据病案号查询病人
export function getListByEmpi(params) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/getListByEmpi',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
