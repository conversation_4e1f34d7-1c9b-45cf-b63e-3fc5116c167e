import request from '@/utils/request'

// 我的相关手术查询
/**
 * @param {string} params.zhuanKeID 专科ID
 * @param {string} params.bingAnHao 病案号
 * @param {string} params.kaiShiSJ 开始时间
 * @param {string} params.jieShuSJ 结束时间
 * @param {string} params.bingRenXM 病人姓名
 * @param {string} params.yiShengRyKID 人员库ID
 * @returns {Promise}
 */
export function getBenRenXGSS(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/getBenRenXGSS',
    method: 'get',
    params
  })
}
// 当日手术病人
/**
 * @param {string} params.bingQuID 病区ID
 * @param {string} params.page 页码，默认1
 * @param {string} params.rows 每页记录数，默认10
 * @param {string} params.shouShuJian 手术间
 * @param {string} params.shouShuRQ 手术日期
 * @param {string} params.zhuYuanHao 住院号
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function getTodaySurgeryPatientList(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getTodaySurgeryPatientList',
    method: 'get',
    params
  })
}
// 手术审批
// 心脏介入诊疗审批
// 外周导管审批
// 手术一览表
// 术后预防使用抗生素病人一览表
/**
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function getPostOpAntibioticPatients(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getPostOpAntibioticPatients',
    method: 'get',
    params
  })
}
// 护排手术修改审批
