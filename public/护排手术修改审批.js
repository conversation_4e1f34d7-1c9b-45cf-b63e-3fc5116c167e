//初始化
public static object Init()
    {
        Dictionary<string, object> dic = new Dictionary<string, object>();
        if (CurrentUser.YHID == -1)
        {
            dic.Add("code", 0);
            dic.Add("msg", "未登录！");
            dic.Add("data", "");
            return dic;
        }
        DateTime ldt_now = Util.f_GetDBDateTime();
        dic.Add("code", 1);
        dic.Add("msg", "");
        dic.Add("kssj", ldt_now.Date.AddDays(-3).ToString("yyyy-MM-dd"));
        dic.Add("jssj", ldt_now.Date.ToString("yyyy-MM-dd"));

        //修改记录的审批流程与现在手术通知单审批（sssp.aspx.cs）流程一致。 
        int YHQX = CurrentUser.GetYYZQX("0200001");//医师工作站权限
        bl_sss_sstzd lb_sstzd = new bl_sss_sstzd();
        string ls_spsslb = "";
        string ls_spgljb = "";
        string ls_sfspzj = "";
        lb_sstzd.f_setSpqx(YHQX, CurrentUser.RYKID.Value, CurrentUser.GZDM, out ls_spsslb, out ls_spgljb, out ls_sfspzj); //获取审批权限
        dic.Add("yhqx", YHQX);
        dic.Add("spsslb", ls_spsslb);
        dic.Add("spsslbmc", lb_sstzd.f_setsslb(ls_spsslb));  //手术类别
        //dic.Add("spgljbmc", ls_spgljb);
        dic.Add("sfspzj", ls_sfspzj);
        dic.Add("sfspzjmc", ls_sfspzj == "1" ? "√" : "×");

        return dic;

    }

//获取审批权限
public int f_setSpqx(int yhqx, long al_rykid, string as_gzdm, out string SPSSLB, out string SPGLJB, out string SFSPZJ)
        {
            SPSSLB = "0";
            SPGLJB = "0";
            SFSPZJ = "0";
            using (CDataBase cdb = new CDataBase("hsz1"))
            {
                cdb.Parameters.Add(":gl_rykid", OracleDbType.Int64).Value = al_rykid;
                string ls_sql = "select sslb,gljb,sfzp from sss_sssp where rykid = :gl_rykid";
                OracleDataReader odr = cdb.SetReader(ls_sql);
                if (odr.HasRows)//维护了个人审批权限
                {
                    odr.Read();
                    if (!odr.IsDBNull(0)) SPSSLB = odr.GetString(0);
                    if (!odr.IsDBNull(1)) SPGLJB = odr.GetString(1);
                    if (!odr.IsDBNull(2)) SFSPZJ = odr.GetString(2);
                    odr.Close();
                }
                else//通用审批权限
                {
                    odr.Close();
                    bl_xtgl_ddlb1 lb_ddlb1 = new bl_xtgl_ddlb1();
                    //是否允许开放手术通知单审批权限
                    if (yhqx > 7 || lb_ddlb1.GetXtgl_ddlb1("SPZJ").DM == "1")
                    {
                        SPSSLB = "9";
                        SPGLJB = "3";
                        SFSPZJ = "1";
                        return 1;
                    }
                    //主治医师可以审批自己以外的1级，副主任医师可以审批自己以外1、2级，主任医师可以审批自己以外1、2、3、4级。审批自己的权限保留给医务处自己设置。
                    switch (as_gzdm)
                    {
                        case "0011"://主任医师
                            SPSSLB = "9";//四级手术
                            break;
                        case "0012"://副主任医师
                            SPSSLB = "7";//二级手术
                            break;
                        case "0013"://主治医师
                            SPSSLB = "6";//一级手术
                            break;
                        default:
                            return 0;
                    }
                    SFSPZJ = f_getSPZJ(al_rykid);
                }
            }
            return 1;
        }

//手术类别
public string f_setsslb(string as_value)
        {
            //2021-06-17 老的手术类别（一二三四特类）停用，启用新的手术级别（一二三四级），共用同一个字段。
            string ls_rtn = "";
            switch (as_value)
            {
                case "1":
                    ls_rtn = "一类手术";
                    break;
                case "2":
                    ls_rtn = "二类手术";
                    break;
                case "3":
                    ls_rtn = "三类手术";
                    break;
                case "4":
                    ls_rtn = "四类手术";
                    break;
                case "5":
                    ls_rtn = "五类手术";
                    break;
                case "6":
                    ls_rtn = "一级手术";
                    break;
                case "7":
                    ls_rtn = "二级手术";
                    break;
                case "8":
                    ls_rtn = "三级手术";
                    break;
                case "9":
                    ls_rtn = "四级手术";
                    break;
                default:
                    ls_rtn = "";
                    break;
            }
            return ls_rtn;
        }


//页面查询获取审批记录    页面已审批选择  开始时间   结束时间
public static object GetXgjlSp(string as_sp, DateTime as_kssj, DateTime as_jssj)
    {
        Dictionary<string, object> dic = new Dictionary<string, object>();
        //"{\"code\":1,\"msg\":\"\",\"data\":null,\"extradata\":null}";//0失败，1成功
        if (CurrentUser.YHID == -1)
        {
            dic.Add("code", 0);
            dic.Add("msg", "未登录！");
            dic.Add("data", "");
            return dic;
        }
        DateTime ldt_kssj = as_kssj.Date;
        DateTime ldt_jssj = as_jssj.Date.AddDays(1);
        bl_sss_sstzd_xgjl lb_sss_sstzd_xgjl = new bl_sss_sstzd_xgjl();
        bl_sss_sstzd lb_sstzd = new bl_sss_sstzd();
        bl_xtgl_bmdm lb_bmdm = new bl_xtgl_bmdm();
        List<m_sss_sstzd_xgjl> list = lb_sss_sstzd_xgjl.GetXgjlByZkid(CurrentUser.ZKID.Value, ldt_kssj, ldt_jssj);  //获取记录
        if (as_sp == "0")//未审批
            list = list.Where(m => string.IsNullOrEmpty(m.KSSPYJ)).ToList();
        else
            list = list.Where(m => !string.IsNullOrEmpty(m.KSSPYJ)).ToList();
        foreach (var m in list)
        {
            m.COLMC = lb_sss_sstzd_xgjl.COLNAMEtoCOLMC(m.COLNAME);
            m.SQSJ_STR = m.SQSJ.ToString("yyyy-MM-dd HH:mm:ss");
            if (m.KSSPSJ.HasValue)
                m.KSSPSJ_STR = m.KSSPSJ.Value.ToString("yyyy-MM-dd HH:mm:ss");
            else
                m.KSSPSJ_STR = "";
            if (m.SSSSPSJ.HasValue)
                m.SSSSPSJ_STR = m.SSSSPSJ.Value.ToString("yyyy-MM-dd HH:mm:ss");
            else
                m.SSSSPSJ_STR = "";
            m.KSSPYJ = lb_sss_sstzd_xgjl.getSpyj(m.KSSPYJ);  //审批意见
            m.SSSSPYJ = lb_sss_sstzd_xgjl.getSpyj(m.SSSSPYJ);  //审批意见

            m_sss_sstzd lm_sss_sstzd = lb_sstzd.GetSss_sstzd(m.TZDID);
            m.BRXM = lm_sss_sstzd.BRXM;
            m.ZKMC = lb_bmdm.GetXtgl_bmdm(lm_sss_sstzd.ZKID).BMMC;
            m.CWH = lm_sss_sstzd.CWH;
            m.NSSSJ = lm_sss_sstzd.NSSSJ.Value.ToString("yyyy-MM-dd");
            m.ZYH = lm_sss_sstzd.ZYH;
        }
        dic.Add("code", 1);
        dic.Add("msg", "");
        dic.Add("data", list);
        return dic;
    }

//获取记录
public List<m_sss_sstzd_xgjl> GetXgjlByZkid(Int64 al_zkid, DateTime ldt_kssj, DateTime ldt_jssj)
        {
            List<m_sss_sstzd_xgjl> lst_sss_sstzd_xgjl = new List<m_sss_sstzd_xgjl>();
            using (HisDbClient cdb = OrmDataBase.GetHisDB("hsz1"))
            {
                string ls_sql = "select * from sss_sstzd_xgjl where ztbz='1' and tzdid in(select tzdid from sss_sstzd where ztbz in ('3','4') and zdzkid=@zdzkid and ((kdsj>=@kssj and kdsj<@jssj) or (nsssj>=@kssj and nsssj<@jssj)))";
                lst_sss_sstzd_xgjl = cdb.SqlQueryable<m_sss_sstzd_xgjl>(ls_sql).AddParameters(new { zdzkid = al_zkid, kssj = ldt_kssj, jssj = ldt_jssj }).OrderBy(m=>m.TZDID).OrderBy(m => m.SQSJ).ToList();
            }
            return lst_sss_sstzd_xgjl;
        }

//审批意见
public string getSpyj(string as_spyj)
        {
            if (string.IsNullOrWhiteSpace(as_spyj))
                return "未审批";

            string ls_spyj = "";
            switch (as_spyj)
            {
                case "1":
                    ls_spyj = "同意";
                    break;
                case "0":
                    ls_spyj = "拒绝";
                    break;
                default:
                    break;
            }
            return ls_spyj;
        }


//做审批操作
 public static object SpXgjl(Int64 al_id, Int64 al_tzdid, string as_spyj)//DateTime ldt_kssj, DateTime ldt_jssj
    {
        Dictionary<string, object> dic = new Dictionary<string, object>();
        //"{\"code\":1,\"msg\":\"\",\"data\":null,\"extradata\":null}";//0失败，1成功
        if (CurrentUser.YHID == -1)
        {
            dic.Add("code", 0);
            dic.Add("msg", "未登录！");
            dic.Add("data", "");
            return dic;
        }

        bl_sss_sstzd_xgjl lb_sss_sstzd_xgjl = new bl_sss_sstzd_xgjl();
        m_sss_sstzd_xgjl lm_sss_sstzd_xgjl = lb_sss_sstzd_xgjl.GetXgjl(al_id);  //根据id获取表sss_sstzd_xgjl记录
        if (lm_sss_sstzd_xgjl.ID == 0 || lm_sss_sstzd_xgjl.TZDID != al_tzdid)
        {
            dic.Add("code", 0);
            dic.Add("msg", "记录不存在！");
            dic.Add("data", "");
            return dic;
        }
        bl_sss_sstzd lb_sstzd = new bl_sss_sstzd();
        m_sss_sstzd lm_sss_sstzd = lb_sstzd.GetSss_sstzd(al_tzdid);
        string ls_spsslb = "";
        string ls_spgljb = "";
        string ls_sfspzj = "";
        int YHQX = CurrentUser.GetYYZQX("0200001");//医师工作站权限
        lb_sstzd.f_setSpqx(YHQX, CurrentUser.RYKID.Value, CurrentUser.GZDM, out ls_spsslb, out ls_spgljb, out ls_sfspzj);    //审批权限
        if (lm_sss_sstzd_xgjl.SQYHID == CurrentUser.YHID)
        {
            if (ls_sfspzj != "1")
            {
                dic.Add("code", 0);
                dic.Add("msg", "你没有权限审批自己的修改记录！");
                dic.Add("data", "");
                return dic;
            }
        }
        else
        {
            if (string.Compare(ls_spsslb, lm_sss_sstzd.SSLB) < 0)
            {
                dic.Add("code", 0);
                dic.Add("msg", "你没有权限审批该修改记录！该通知单为" + lb_sstzd.f_setsslb(lm_sss_sstzd.SSLB));
                dic.Add("data", "");
                return dic;
            }
        }

        m_sss_sstzd_xgjl lm_xgjl = lb_sss_sstzd_xgjl.GetXgjl(al_id);
        if (!string.IsNullOrEmpty(lm_xgjl.SSSSPYJ))
        {
            dic.Add("code", 0);
            dic.Add("msg", "手术室已审批，无法再进行科室审批！");
            dic.Add("data", "");
            return dic;
        }

        if (as_spyj == "1")
        {
            //只能有一条同意的审批记录
            /*List<m_sss_sstzd_xgjl> lst_xgjl = lb_sss_sstzd_xgjl.GetXgjlByTzdid(al_tzdid);
            List<m_sss_sstzd_xgjl> lst_xgjl_ysp = lst_xgjl.AsEnumerable().Where(o => o.KSSPYJ == "1" && o.TZDID == al_tzdid && lm_xgjl.COLNAME == o.COLNAME && o.ZTBZ == "1" && o.ID != al_id).ToList();
            if (lst_xgjl_ysp.Count >= 1)
            {
                dic.Add("code", 0);
                dic.Add("msg", "该修改项目已存在科室审批意见为“同意”的审批记录！");
                dic.Add("data", "");
                return dic;
            }*/
        }

        lm_sss_sstzd_xgjl.KSSPSJ = Util.f_GetDBDateTime();
        lm_sss_sstzd_xgjl.KSSPYHID = CurrentUser.YHID;
        lm_sss_sstzd_xgjl.KSSPYHXM = CurrentUser.YHXM;
        lm_sss_sstzd_xgjl.KSSPYJ = as_spyj;
        string ls_msg = "";
        bool lb_rtn = lb_sss_sstzd_xgjl.SpXgjl(lm_sss_sstzd_xgjl, out ls_msg);  //审批操作
        if (lb_rtn)
        {
            string ls_mzhzMsg = "";
            if (lm_sss_sstzd.RJSS == "1" && as_spyj == "1" && lm_sss_sstzd_xgjl.COLNAME == "mzhz" &&
                lm_sss_sstzd_xgjl.OLDDM == "0" && lm_sss_sstzd_xgjl.NEWDM == "1")//日间勾选麻醉会诊要发送短信
            {
                lb_sstzd.mzhzSendMsg(lm_sss_sstzd.BLID.Value, lm_sss_sstzd.BRBH, lm_sss_sstzd.BQID, lm_sss_sstzd_xgjl.SQYHID, CurrentUser.BMID, out ls_mzhzMsg);
            }

            dic.Add("code", 1);
            dic.Add("msg", "成功");
            dic.Add("data", "");
            return dic;
        }
        else
        {
            dic.Add("code", 0);
            dic.Add("msg", "" + ls_msg);
            dic.Add("data", "");
            return dic;
        }
    }
//根据id获取表sss_sstzd_xgjl记录
 public m_sss_sstzd_xgjl GetXgjl(Int64 al_id)
        {
            m_sss_sstzd_xgjl lm_sss_sstzd_xgjl;
            using (HisDbClient cdb = OrmDataBase.GetHisDB("hsz1"))
            {
                lm_sss_sstzd_xgjl = cdb.Queryable<m_sss_sstzd_xgjl>()
                    .Where(it => it.ID == al_id && it.ZTBZ == "1")
                    .First();
            }
            return lm_sss_sstzd_xgjl;
        }

//审批操作
public bool SpXgjl(m_sss_sstzd_xgjl lm_sss_sstzd_xgjl, out string msg)
        {
            msg = "";
            using (HisDbClient cdb = OrmDataBase.GetHisDB("ylgz3"))
            {
                try
                {
                    cdb.Ado.BeginTran();
                    int rownum = cdb.Updateable<m_sss_sstzd_xgjl>(lm_sss_sstzd_xgjl).UpdateColumns(m => new { m.KSSPYJ, m.KSSPSJ, m.KSSPYHID, m.KSSPYHXM }).ExecuteCommand();
                    if (rownum == 1)
                    {
                        msg = "数据库操作成功!";
                        cdb.Ado.CommitTran();
                        return true;
                    }
                    else
                    {
                        msg = "数据库操作失败!";
                        cdb.Ado.RollbackTran();
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    cdb.Ado.RollbackTran();
                    msg = ex.ToString();
                    return false;
                }
            }
        }


//麻醉会诊发送短信
public bool mzhzSendMsg(Int64 al_blid, string as_brbh, Int64 al_bqid, Int64 al_yhid, Int64 al_bmid, out string ls_res)
        {
            bl_xtgl_bmdm lb_xtgl_bmdm = new bl_xtgl_bmdm();
            //lb_xtgl_bmdm.GetXtglbmdmsByBmxhgl()
            EHR1.Model.m_xtgl_bmdm lm_xtgl_bmdm = lb_xtgl_bmdm.GetXtgl_bmdm(al_bqid);
            bl_yl_ddlbn lb_yl_ddlbn = new bl_yl_ddlbn();
            m_yl_ddlbn lm_yl_ddlbn = lb_yl_ddlbn.GetYl_ddlbn("0000", "MZHZ");
            if (string.IsNullOrEmpty(lm_yl_ddlbn.DM) || lm_yl_ddlbn.ZTBZ != "1")
            {
                ls_res = "该短信功能已关闭";
                return false;
            }
            string ls_dxnr = "";
            string ls_didian = "";
            string ls_sj = "";
            if (lm_xtgl_bmdm.YQDM == "01")//老院
            {
                lm_yl_ddlbn = lb_yl_ddlbn.GetYl_ddlbn("MZHZ", "01");
                if (string.IsNullOrEmpty(lm_yl_ddlbn.DM) || lm_yl_ddlbn.ZTBZ != "1")
                {
                    ls_res = "该短信功能已关闭";
                    return false;
                }
                else
                {
                    ls_dxnr = lm_yl_ddlbn.MC;//请入院前一天或当日到老院区门诊3楼内镜中心麻醉会诊处麻醉评估（工作日9点―11点30，14点半-16点半）
                    ls_didian = "老院区门诊3楼内镜中心麻醉会诊处";
                    ls_sj = "工作日9点―11点30，14点半-16点半";
                }
            }
            else //02新院
            {
                lm_yl_ddlbn = lb_yl_ddlbn.GetYl_ddlbn("MZHZ", "02");
                if (string.IsNullOrEmpty(lm_yl_ddlbn.DM) || lm_yl_ddlbn.ZTBZ != "1")
                {
                    ls_res = "该短信功能已关闭";
                    return false;
                }
                else
                {
                    ls_dxnr = lm_yl_ddlbn.MC;//请入院前一天或当天到门诊一号楼三层A16麻醉门诊进行麻醉评估,工作日8-12点，13点半-20点半，周日14-17点
                    ls_didian = "门诊一号楼三层A16麻醉门诊";
                    ls_sj = "工作日8-12点，13点半-20点半，周日14-17点";
                }
            }
            bl_cw_khxx lb_cw_khxx = new bl_cw_khxx();
            m_cw_khxx lm_cw_khxx = lb_cw_khxx.GetCw_khxx(as_brbh);
            string sjhm = "";
            if (string.IsNullOrEmpty(lm_cw_khxx.YDDH) == false && lm_cw_khxx.YDDH.Length == 11 && lm_cw_khxx.YDDH.Substring(0, 1) == "1")//简单验证手机号码
            {
                sjhm = lm_cw_khxx.YDDH;
            }
            else
            {
                if (string.IsNullOrEmpty(lm_cw_khxx.LXDH) == false && lm_cw_khxx.LXDH.Length == 11 && lm_cw_khxx.LXDH.Substring(0, 1) == "1")//简单验证手机号码
                {
                    sjhm = lm_cw_khxx.LXDH;
                }
            }

            if (string.IsNullOrEmpty(sjhm))
            {
                ls_res = "患者手机号码为空";
                return false;
            }
            /*bl_Csm lb_csm = new bl_Csm();
            //xxlb=138， select * from xtgl_ddlbn where lb = '2002' and dm = '138' 电子病历
            string ls_xxlb = "138";
            long rtn = lb_csm.uf_notify(ls_dxnr, "" + sjhm, ls_xxlb, 1, "", out ls_res, al_yhid, al_bmid);
            if (rtn > 0)
            {
                ls_res = "提醒病人" + ls_dxnr;
                return true;
            }
            else
                return false;*/

            //微服务多渠道推送
            bl_wfw lb_wfw = new bl_wfw();
            bl_xtgl_yhxx lb_yhxx = new bl_xtgl_yhxx();
            m_xtgl_yhxx lm_xtgl_yhxx_current = lb_yhxx.GetXtgl_yhxx(al_yhid);
            public1.Model.m_wfw_xxts lm_wfw_xxts = new public1.Model.m_wfw_xxts();
            List<public1.Model.m_FaSongDXItem> faSongDXs = new List<public1.Model.m_FaSongDXItem>();
            public1.Model.m_FaSongDXItem faSongDX = new public1.Model.m_FaSongDXItem();
            faSongDX.bingRenBH = as_brbh;
            faSongDX.shouJiHM = sjhm;
            faSongDXs.Add(faSongDX);
            lm_wfw_xxts.faSongDX = faSongDXs;
            JObject gjzzd = new JObject();//请入院前一天或当日到@diDian进行麻醉评估（@shiJian）。
            gjzzd.Add("diDian", ls_didian);
            gjzzd.Add("shiJian", ls_sj);
            lm_wfw_xxts.guanJianZZD = gjzzd;
            lm_wfw_xxts.qingQiuFWM = "020";
            lm_wfw_xxts.xiaoXiLXBH = 90007;
            public1.Model.m_return lm_return = lb_wfw.wfw_SendMessage(lm_wfw_xxts, lm_xtgl_yhxx_current);
            if (lm_return.hasError != 0)
            {
                ls_res = lm_return.errorMessage;//失败
                return false;
            }
            else
            {
                if (lm_return.data.returnCode != 0)
                {
                    ls_res = lm_return.data.returnMessage;//失败
                    return false;
                }
                else
                {
                    ls_res = "提醒病人" + ls_dxnr;
                    return true;
                }
            }
        }